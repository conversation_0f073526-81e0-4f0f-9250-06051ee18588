import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:getdoctor/features/domain/entities/doctor_entity.dart';

class DoctorModel extends DoctorEntity {
  DoctorModel({
    required String doctorName,
    required String specialist,
    required String timing,
    required String department,
    required String profileImage,
    required String gender,
    required String phoneNumber,
    required String email,
    required String descriptionDetails,
    required String hospitalAddress,
    required String qualification,
    required String doctorId,
  })
      : super(
          doctor<PERSON>ame: doctor<PERSON><PERSON>,
          specialist: specialist,
          timing: timing,
          department: department,
          profileImage: profileImage,
          gender: gender,
          phoneNumber: phoneNumber,
          email: email,
          descriptionDetails: descriptionDetails,
          hospitalAddress: hospitalAddress,
          qualification: qualification,
          doctorId: doctorId,
        );

  factory DoctorModel.fromSnapShot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return DoctorModel(
      doctorName: data['doctorName'] ?? '',
      specialist: data['specialist'] ?? '',
      timing: data['timing'] ?? '',
      department: data['department'] ?? '',
      profileImage: data['profileImage'] ?? '',
      gender: data['gender'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      email: data['email'] ?? '',
      descriptionDetails: data['descriptionDetails'] ?? '',
      hospitalAddress: data['hospitalAddress'] ?? '',
      qualification: data['qualification'] ?? '',
      doctorId: data['doctorId'] ?? '',
    );
  }
}

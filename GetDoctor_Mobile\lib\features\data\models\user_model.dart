import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:getdoctor/features/domain/entities/user_entity.dart';

class UserModel extends UserEntity {
  UserModel({
    required String uid,
    required String name,
    required String email,
    required String profileUrl,
    required String phoneNumber,
    required bool isOnline,
    required String status,
    required String accountType,
    required String isHide,
  }) : super(
          uid: uid,
          name: name,
          email: email,
          profileUrl: profileUrl,
          phoneNumber: phoneNumber,
          isOnline: isOnline,
          status: status,
          accountType: accountType,
          isHide: isHide,
        );

  factory UserModel.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return UserModel(
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      uid: data['uid'] ?? '',
      profileUrl: data['profileUrl'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      isOnline: data['isOnline'] ?? false,
      status: data['status'] ?? '',
      accountType: data['accountType'] ?? '',
      isHide: data['isHide'] ?? '',
    );
  }

  Map<String, dynamic> toDocument() {
    return {
      "name": name,
      "email": email,
      "uid": uid,
      "profileUrl": profileUrl,
      "phoneNumber": phoneNumber,
      "isOnline": isOnline,
      "status": status,
      "accountType": accountType,
      "isHide": isHide,
    };
  }
}

{"logs": [{"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-hy_values-hy.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-hy\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,586,690,840,972,1095,1204,1367,1471,1635,1767,1925,2087,2148,2211", "endColumns": "101,160,129,103,149,131,122,108,162,103,163,131,157,161,60,62,77", "endOffsets": "294,455,585,689,839,971,1094,1203,1366,1470,1634,1766,1924,2086,2147,2210,2288"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,881,1046,1180,1288,1442,1578,1705,1969,2136,2244,2412,2548,2710,2876,2941,3008", "endColumns": "105,164,133,107,153,135,126,112,166,107,167,135,161,165,64,66,81", "endOffsets": "876,1041,1175,1283,1437,1573,1700,1813,2131,2239,2407,2543,2705,2871,2936,3003,3085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,262,373", "endColumns": "103,102,110,102", "endOffsets": "154,257,368,471"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3090,3194,3297,3408", "endColumns": "103,102,110,102", "endOffsets": "3189,3292,3403,3506"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,3511", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,3607"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-hy\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1818", "endColumns": "150", "endOffsets": "1964"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-zh-rHK_values-zh-rHK.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,2911", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,3007"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-zh-rHK\\values-zh-rHK.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2542,2625,2717,2818", "endColumns": "82,91,100,92", "endOffsets": "2620,2712,2813,2906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "103", "endOffsets": "302"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1580", "endColumns": "107", "endOffsets": "1683"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rHK\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,940,1029,1135,1232,1357,1468,1571,1675,1726,1779", "endColumns": "96,123,110,97,102,111,97,88,105,96,124,110,102,103,50,52,67", "endOffsets": "293,417,528,626,729,841,939,1028,1134,1231,1356,1467,1570,1674,1725,1778,1846"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,817,945,1060,1162,1269,1385,1487,1688,1798,1899,2028,2143,2250,2358,2413,2470", "endColumns": "100,127,114,101,106,115,101,92,109,100,128,114,106,107,54,56,71", "endOffsets": "812,940,1055,1157,1264,1380,1482,1575,1793,1894,2023,2138,2245,2353,2408,2465,2537"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-iw_values-iw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-iw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1717", "endColumns": "117", "endOffsets": "1830"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,3222", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,3318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-iw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,855,1009,1134,1238,1377,1502,1614,1835,1971,2075,2220,2343,2477,2622,2682,2742", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "850,1004,1129,1233,1372,1497,1609,1712,1966,2070,2215,2338,2472,2617,2677,2737,2818"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2823,2914,3014,3120", "endColumns": "90,99,105,101", "endOffsets": "2909,3009,3115,3217"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-sl_values-sl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,3426", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,3522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1780", "endColumns": "139", "endOffsets": "1915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3002,3108,3212,3324", "endColumns": "105,103,111,101", "endOffsets": "3103,3207,3319,3421"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "778,884,1049,1178,1285,1432,1562,1676,1920,2078,2185,2352,2482,2634,2786,2856,2918", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "879,1044,1173,1280,1427,1557,1671,1775,2073,2180,2347,2477,2629,2781,2851,2913,2997"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-nb_values-nb.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1790", "endColumns": "129", "endOffsets": "1915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,3415", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,3511"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,887,1046,1172,1281,1437,1567,1687,1920,2074,2181,2342,2470,2612,2788,2855,2917", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "882,1041,1167,1276,1432,1562,1682,1785,2069,2176,2337,2465,2607,2783,2850,2912,2990"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2995,3105,3206,3318", "endColumns": "109,100,111,96", "endOffsets": "3100,3201,3313,3410"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-en-rCA_values-en-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "773,871,968,1077", "endColumns": "97,96,108,98", "endOffsets": "866,963,1072,1171"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-en-rCA\\values-en-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,773", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,657,1176", "endColumns": "95,101,98,98,103,101,115,100", "endOffsets": "146,248,347,446,550,652,768,1272"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-bs_values-bs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,3391", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,3487"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1772", "endColumns": "140", "endOffsets": "1908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,259,373", "endColumns": "103,99,113,99", "endOffsets": "154,254,368,468"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2973,3077,3177,3291", "endColumns": "103,99,113,99", "endOffsets": "3072,3172,3286,3386"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "780,886,1046,1172,1282,1432,1558,1670,1913,2067,2174,2335,2462,2612,2758,2826,2888", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "881,1041,1167,1277,1427,1553,1665,1767,2062,2169,2330,2457,2607,2753,2821,2883,2968"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-es-rUS_values-es-rUS.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,3548", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,3644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "141", "endOffsets": "340"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1822", "endColumns": "145", "endOffsets": "1963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,266,381", "endColumns": "108,101,114,105", "endOffsets": "159,261,376,482"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3116,3225,3327,3442", "endColumns": "108,101,114,105", "endOffsets": "3220,3322,3437,3543"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-es-rUS\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,301,463,591,695,852,979,1098,1200,1367,1472,1638,1767,1940,2114,2180,2238", "endColumns": "103,161,127,103,156,126,118,101,166,104,165,128,172,173,65,57,73", "endOffsets": "300,462,590,694,851,978,1097,1199,1366,1471,1637,1766,1939,2113,2179,2237,2311"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,895,1061,1193,1301,1462,1593,1716,1968,2139,2248,2418,2551,2728,2906,2976,3038", "endColumns": "107,165,131,107,160,130,122,105,170,108,169,132,176,177,69,61,77", "endOffsets": "890,1056,1188,1296,1457,1588,1711,1817,2134,2243,2413,2546,2723,2901,2971,3033,3111"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ka_values-ka.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,3423", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,3519"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ka\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,439,563,669,819,949,1067,1171,1340,1444,1595,1719,1876,2011,2073,2130", "endColumns": "100,144,123,105,149,129,117,103,168,103,150,123,156,134,61,56,71", "endOffsets": "293,438,562,668,818,948,1066,1170,1339,1443,1594,1718,1875,2010,2072,2129,2201"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "779,884,1033,1161,1271,1425,1559,1681,1932,2105,2213,2368,2496,2657,2796,2862,2923", "endColumns": "104,148,127,109,153,133,121,107,172,107,154,127,160,138,65,60,75", "endOffsets": "879,1028,1156,1266,1420,1554,1676,1784,2100,2208,2363,2491,2652,2791,2857,2918,2994"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ka\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1789", "endColumns": "142", "endOffsets": "1927"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2999,3105,3208,3318", "endColumns": "105,102,109,104", "endOffsets": "3100,3203,3313,3418"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-as_values-as.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,913,1066,1189,1299,1429,1551,1664,1902,2045,2154,2304,2429,2562,2715,2775,2841", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "908,1061,1184,1294,1424,1546,1659,1771,2040,2149,2299,2424,2557,2710,2770,2836,2922"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,3355", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,3451"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-as\\values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2927,3035,3141,3249", "endColumns": "107,105,107,105", "endOffsets": "3030,3136,3244,3350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-as\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1776", "endColumns": "125", "endOffsets": "1897"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-tl_values-tl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-tl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1848", "endColumns": "144", "endOffsets": "1988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-tl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,468,602,707,861,993,1111,1220,1395,1498,1672,1806,1964,2139,2203,2265", "endColumns": "102,171,133,104,153,131,117,108,174,102,173,133,157,174,63,61,76", "endOffsets": "295,467,601,706,860,992,1110,1219,1394,1497,1671,1805,1963,2138,2202,2264,2341"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "789,896,1072,1210,1319,1477,1613,1735,1993,2172,2279,2457,2595,2757,2936,3004,3070", "endColumns": "106,175,137,108,157,135,121,112,178,106,177,137,161,178,67,65,80", "endOffsets": "891,1067,1205,1314,1472,1608,1730,1843,2167,2274,2452,2590,2752,2931,2999,3065,3146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,263,374", "endColumns": "102,104,110,104", "endOffsets": "153,258,369,474"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3151,3254,3359,3470", "endColumns": "102,104,110,104", "endOffsets": "3249,3354,3465,3570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,3575", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,3671"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-zh-rTW_values-zh-rTW.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1580", "endColumns": "102", "endOffsets": "1678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "716,817,945,1060,1162,1269,1385,1485,1683,1793,1894,2023,2138,2240,2348,2404,2461", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "812,940,1055,1157,1264,1380,1480,1575,1788,1889,2018,2133,2235,2343,2399,2456,2530"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,2905", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,3001"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2535,2619,2711,2812", "endColumns": "83,91,100,92", "endOffsets": "2614,2706,2807,2900"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-fi_values-fi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,789", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,351,456,561,673,3372", "endColumns": "95,101,97,104,104,111,115,100", "endOffsets": "146,248,346,451,556,668,784,3468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-fi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,449,576,678,817,939,1051,1154,1291,1393,1538,1660,1804,1939,2001,2067", "endColumns": "106,148,126,101,138,121,111,102,136,101,144,121,143,134,61,65,78", "endOffsets": "299,448,575,677,816,938,1050,1153,1290,1392,1537,1659,1803,1938,2000,2066,2145"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "789,900,1053,1184,1290,1433,1559,1675,1932,2073,2179,2328,2454,2602,2741,2807,2877", "endColumns": "110,152,130,105,142,125,115,106,140,105,148,125,147,138,65,69,82", "endOffsets": "895,1048,1179,1285,1428,1554,1670,1777,2068,2174,2323,2449,2597,2736,2802,2872,2955"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-fi\\values-fi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,368", "endColumns": "102,100,108,98", "endOffsets": "153,254,363,462"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2960,3063,3164,3273", "endColumns": "102,100,108,98", "endOffsets": "3058,3159,3268,3367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-fi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1782", "endColumns": "149", "endOffsets": "1927"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-uz_values-uz.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,173,282,392", "endColumns": "117,108,109,105", "endOffsets": "168,277,387,493"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3032,3150,3259,3369", "endColumns": "117,108,109,105", "endOffsets": "3145,3254,3364,3470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-uz\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1788", "endColumns": "149", "endOffsets": "1933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,3475", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,3571"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-uz\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,440,565,670,811,940,1056,1158,1326,1430,1585,1713,1863,2021,2083,2140", "endColumns": "100,145,124,104,140,128,115,101,167,103,154,127,149,157,61,56,75", "endOffsets": "293,439,564,669,810,939,1055,1157,1325,1429,1584,1712,1862,2020,2082,2139,2215"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "791,896,1046,1175,1284,1429,1562,1682,1938,2110,2218,2377,2509,2663,2825,2891,2952", "endColumns": "104,149,128,108,144,132,119,105,171,107,158,131,153,161,65,60,79", "endOffsets": "891,1041,1170,1279,1424,1557,1677,1783,2105,2213,2372,2504,2658,2820,2886,2947,3027"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-fr_values-fr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-fr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "160", "endOffsets": "355"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1845", "endColumns": "164", "endOffsets": "2005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,3617", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,3713"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3184,3291,3393,3512", "endColumns": "106,101,118,104", "endOffsets": "3286,3388,3507,3612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-fr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,471,597,702,869,998,1115,1224,1398,1506,1687,1819,1975,2150,2219,2282", "endColumns": "101,175,125,104,166,128,116,108,173,107,180,131,155,174,68,62,79", "endOffsets": "294,470,596,701,868,997,1114,1223,1397,1505,1686,1818,1974,2149,2218,2281,2361"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,888,1068,1198,1307,1478,1611,1732,2010,2188,2300,2485,2621,2781,2960,3033,3100", "endColumns": "105,179,129,108,170,132,120,112,177,111,184,135,159,178,72,66,83", "endOffsets": "883,1063,1193,1302,1473,1606,1727,1840,2183,2295,2480,2616,2776,2955,3028,3095,3179"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-b+sr+Latn_values-b+sr+Latn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2956,3067,3167,3280", "endColumns": "110,99,112,97", "endOffsets": "3062,3162,3275,3373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-b+sr+Latn\\values-b+sr+Latn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,3378", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,3474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "202", "endColumns": "125", "endOffsets": "327"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1778", "endColumns": "129", "endOffsets": "1903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-b+sr+Latn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "200,303,457,580,686,836,959,1067,1165,1310,1413,1569,1692,1837,1976,2040,2101", "endColumns": "102,153,122,105,149,122,107,97,144,102,155,122,144,138,63,60,75", "endOffsets": "302,456,579,685,835,958,1066,1164,1309,1412,1568,1691,1836,1975,2039,2100,2176"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,888,1046,1173,1283,1437,1564,1676,1908,2057,2164,2324,2451,2600,2743,2811,2876", "endColumns": "106,157,126,109,153,126,111,101,148,106,159,126,148,142,67,64,79", "endOffsets": "883,1041,1168,1278,1432,1559,1671,1773,2052,2159,2319,2446,2595,2738,2806,2871,2951"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-az_values-az.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-az\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "157", "endOffsets": "352"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1794", "endColumns": "161", "endOffsets": "1951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,3439", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,3535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-az\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,449,578,684,826,955,1071,1173,1335,1441,1586,1719,1859,2011,2071,2132", "endColumns": "104,150,128,105,141,128,115,101,161,105,144,132,139,151,59,60,76", "endOffsets": "297,448,577,683,825,954,1070,1172,1334,1440,1585,1718,1858,2010,2070,2131,2208"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,891,1046,1179,1289,1435,1568,1688,1956,2122,2232,2381,2518,2662,2818,2882,2947", "endColumns": "108,154,132,109,145,132,119,105,165,109,148,136,143,155,63,64,80", "endOffsets": "886,1041,1174,1284,1430,1563,1683,1789,2117,2227,2376,2513,2657,2813,2877,2942,3023"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3028,3124,3228,3336", "endColumns": "95,103,107,102", "endOffsets": "3119,3223,3331,3434"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-pt_values-pt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,1222", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,1318"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-pt\\values-pt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "790,905,1004,1116", "endColumns": "114,98,111,105", "endOffsets": "900,999,1111,1217"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-hu_values-hu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-hu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,480,614,719,883,1017,1135,1241,1407,1511,1692,1825,1993,2161,2228,2292", "endColumns": "106,179,133,104,163,133,117,105,165,103,180,132,167,167,66,63,83", "endOffsets": "299,479,613,718,882,1016,1134,1240,1406,1510,1691,1824,1992,2160,2227,2291,2375"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "777,888,1072,1210,1319,1487,1625,1747,2034,2204,2312,2497,2634,2806,2978,3049,3117", "endColumns": "110,183,137,108,167,137,121,109,169,107,184,136,171,171,70,67,87", "endOffsets": "883,1067,1205,1314,1482,1620,1742,1852,2199,2307,2492,2629,2801,2973,3044,3112,3200"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,252,367", "endColumns": "95,100,114,103", "endOffsets": "146,247,362,466"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3205,3301,3402,3517", "endColumns": "95,100,114,103", "endOffsets": "3296,3397,3512,3616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-hu\\values-hu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,777", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,873"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,356,457,560,667,3621", "endColumns": "96,101,101,100,102,106,109,100", "endOffsets": "147,249,351,452,555,662,772,3717"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-hu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "172", "endOffsets": "367"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1857", "endColumns": "176", "endOffsets": "2029"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-hi_values-hi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-hi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,575,683,830,956,1064,1172,1325,1430,1592,1718,1855,2004,2063,2126", "endColumns": "103,155,121,107,146,125,107,107,152,104,161,125,136,148,58,62,83", "endOffsets": "296,452,574,682,829,955,1063,1171,1324,1429,1591,1717,1854,2003,2062,2125,2209"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "808,916,1076,1202,1314,1465,1595,1707,1965,2122,2231,2397,2527,2668,2821,2884,2951", "endColumns": "107,159,125,111,150,129,111,111,156,108,165,129,140,152,62,66,87", "endOffsets": "911,1071,1197,1309,1460,1590,1702,1814,2117,2226,2392,2522,2663,2816,2879,2946,3034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,375", "endColumns": "105,101,111,102", "endOffsets": "156,258,370,473"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3039,3145,3247,3359", "endColumns": "105,101,111,102", "endOffsets": "3140,3242,3354,3457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,3462", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,3558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-hi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1819", "endColumns": "145", "endOffsets": "1960"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ja_values-ja.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ja\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,423,539,641,769,885,986,1081,1211,1308,1437,1552,1668,1784,1840,1895", "endColumns": "99,129,115,101,127,115,100,94,129,96,128,114,115,115,55,54,66", "endOffsets": "292,422,538,640,768,884,985,1080,1210,1307,1436,1551,1667,1783,1839,1894,1961"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "724,828,962,1082,1188,1320,1440,1545,1766,1900,2001,2134,2253,2373,2493,2553,2612", "endColumns": "103,133,119,105,131,119,104,98,133,100,132,118,119,119,59,58,70", "endOffsets": "823,957,1077,1183,1315,1435,1540,1639,1895,1996,2129,2248,2368,2488,2548,2607,2678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ja\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "117", "endOffsets": "312"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1644", "endColumns": "121", "endOffsets": "1761"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,724", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,820"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,437,530,623,3067", "endColumns": "91,99,93,95,92,92,100,100", "endOffsets": "142,242,336,432,525,618,719,3163"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ja\\values-ja.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,148,243,344", "endColumns": "92,94,100,94", "endOffsets": "143,238,339,434"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2683,2776,2871,2972", "endColumns": "92,94,100,94", "endOffsets": "2771,2866,2967,3062"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-in_values-in.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2986,3086,3184,3293", "endColumns": "99,97,108,100", "endOffsets": "3081,3179,3288,3389"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "785,892,1056,1182,1288,1443,1570,1685,1923,2089,2194,2358,2484,2639,2783,2847,2907", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "887,1051,1177,1283,1438,1565,1680,1786,2084,2189,2353,2479,2634,2778,2842,2902,2981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,3394", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,3490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1791", "endColumns": "131", "endOffsets": "1918"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-sw_values-sw.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,3474", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,3570"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "778,882,1038,1163,1272,1435,1560,1679,1939,2104,2210,2364,2491,2641,2800,2868,2943", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "877,1033,1158,1267,1430,1555,1674,1788,2099,2205,2359,2486,2636,2795,2863,2938,3034"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1793", "endColumns": "145", "endOffsets": "1934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3039,3153,3254,3371", "endColumns": "113,100,116,102", "endOffsets": "3148,3249,3366,3469"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-cs_values-cs.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-cs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1780", "endColumns": "142", "endOffsets": "1918"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,786", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,455,560,667,3458", "endColumns": "97,101,100,98,104,106,118,100", "endOffsets": "148,250,351,450,555,662,781,3554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-cs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,572,678,827,950,1058,1155,1326,1433,1593,1717,1874,2025,2089,2152", "endColumns": "101,155,120,105,148,122,107,96,170,106,159,123,156,150,63,62,81", "endOffsets": "294,450,571,677,826,949,1057,1154,1325,1432,1592,1716,1873,2024,2088,2151,2233"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,892,1052,1177,1287,1440,1567,1679,1923,2098,2209,2373,2501,2662,2817,2885,2952", "endColumns": "105,159,124,109,152,126,111,100,174,110,163,127,160,154,67,66,85", "endOffsets": "887,1047,1172,1282,1435,1562,1674,1775,2093,2204,2368,2496,2657,2812,2880,2947,3033"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-cs\\values-cs.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,157,260,374", "endColumns": "101,102,113,100", "endOffsets": "152,255,369,470"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3038,3140,3243,3357", "endColumns": "101,102,113,100", "endOffsets": "3135,3238,3352,3453"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ca_values-ca.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,3470", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,3566"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3034,3147,3250,3361", "endColumns": "112,102,110,108", "endOffsets": "3142,3245,3356,3465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,891,1043,1170,1279,1429,1556,1679,1922,2093,2202,2361,2492,2656,2814,2879,2947", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "886,1038,1165,1274,1424,1551,1674,1782,2088,2197,2356,2487,2651,2809,2874,2942,3029"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1787", "endColumns": "134", "endOffsets": "1917"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-kn_values-kn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,3467", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,3563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-kn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1808", "endColumns": "145", "endOffsets": "1949"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-kn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,466,595,706,845,970,1074,1169,1315,1424,1585,1716,1857,2010,2075,2134", "endColumns": "106,165,128,110,138,124,103,94,145,108,160,130,140,152,64,58,80", "endOffsets": "299,465,594,705,844,969,1073,1168,1314,1423,1584,1715,1856,2009,2074,2133,2214"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "800,911,1081,1214,1329,1472,1601,1709,1954,2104,2217,2382,2517,2662,2819,2888,2951", "endColumns": "110,169,132,114,142,128,107,98,149,112,164,134,144,156,68,62,84", "endOffsets": "906,1076,1209,1324,1467,1596,1704,1803,2099,2212,2377,2512,2657,2814,2883,2946,3031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,269,385", "endColumns": "113,99,115,100", "endOffsets": "164,264,380,481"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3036,3150,3250,3366", "endColumns": "113,99,115,100", "endOffsets": "3145,3245,3361,3462"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-pt-rPT_values-pt-rPT.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3121,3237,3336,3448", "endColumns": "115,98,111,102", "endOffsets": "3232,3331,3443,3546"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,892,1055,1183,1291,1459,1587,1709,1963,2151,2259,2429,2560,2719,2897,2965,3034", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "887,1050,1178,1286,1454,1582,1704,1813,2146,2254,2424,2555,2714,2892,2960,3029,3116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-pt-rPT\\values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,3551", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,3647"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pt-rPT\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1818", "endColumns": "144", "endOffsets": "1958"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-sr_values-sr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,3373", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,3469"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,447,569,675,825,948,1056,1154,1299,1402,1558,1681,1826,1964,2028,2089", "endColumns": "101,151,121,105,149,122,107,97,144,102,155,122,144,137,63,60,75", "endOffsets": "294,446,568,674,824,947,1055,1153,1298,1401,1557,1680,1825,1963,2027,2088,2164"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,887,1043,1169,1279,1433,1560,1672,1904,2053,2160,2320,2447,2596,2738,2806,2871", "endColumns": "105,155,125,109,153,126,111,101,148,106,159,126,148,141,67,64,79", "endOffsets": "882,1038,1164,1274,1428,1555,1667,1769,2048,2155,2315,2442,2591,2733,2801,2866,2946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,266,379", "endColumns": "110,99,112,97", "endOffsets": "161,261,374,472"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2951,3062,3162,3275", "endColumns": "110,99,112,97", "endOffsets": "3057,3157,3270,3368"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1774", "endColumns": "129", "endOffsets": "1899"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-en-rIN_values-en-rIN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,1177", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,1273"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-en-rIN\\values-en-rIN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "774,872,969,1078", "endColumns": "97,96,108,98", "endOffsets": "867,964,1073,1172"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-en-rAU_values-en-rAU.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "774,872,969,1078", "endColumns": "97,96,108,98", "endOffsets": "867,964,1073,1172"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,1177", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,1273"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-v21_values-v21.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-zu_values-zu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,785", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,257,356,459,565,672,3514", "endColumns": "97,103,98,102,105,106,112,100", "endOffsets": "148,252,351,454,560,667,780,3610"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,472,603,703,867,992,1112,1218,1374,1480,1641,1768,1922,2075,2132,2197", "endColumns": "106,171,130,99,163,124,119,105,155,105,160,126,153,152,56,64,80", "endOffsets": "299,471,602,702,866,991,1111,1217,1373,1479,1640,1767,1921,2074,2131,2196,2277"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "785,896,1072,1207,1311,1479,1608,1732,1974,2134,2244,2409,2540,2698,2855,2916,2985", "endColumns": "110,175,134,103,167,128,123,109,159,109,164,130,157,156,60,68,84", "endOffsets": "891,1067,1202,1306,1474,1603,1727,1837,2129,2239,2404,2535,2693,2850,2911,2980,3065"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1842", "endColumns": "131", "endOffsets": "1969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-zu\\values-zu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,275,387", "endColumns": "111,107,111,111", "endOffsets": "162,270,382,494"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3070,3182,3290,3402", "endColumns": "111,107,111,111", "endOffsets": "3177,3285,3397,3509"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ml_values-ml.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,267,378", "endColumns": "108,102,110,103", "endOffsets": "159,262,373,477"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3153,3262,3365,3476", "endColumns": "108,102,110,103", "endOffsets": "3257,3360,3471,3575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ml\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1850", "endColumns": "159", "endOffsets": "2005"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ml\\values-ml.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,792", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,362,466,569,670,3580", "endColumns": "101,102,101,103,102,100,121,100", "endOffsets": "152,255,357,461,564,665,787,3676"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ml\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,308,483,618,735,895,1016,1117,1219,1397,1509,1679,1811,1956,2113,2173,2238", "endColumns": "114,174,134,116,159,120,100,101,177,111,169,131,144,156,59,64,87", "endOffsets": "307,482,617,734,894,1015,1116,1218,1396,1508,1678,1810,1955,2112,2172,2237,2325"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,911,1090,1229,1350,1514,1639,1744,2010,2192,2308,2482,2618,2767,2928,2992,3061", "endColumns": "118,178,138,120,163,124,104,105,181,115,173,135,148,160,63,68,91", "endOffsets": "906,1085,1224,1345,1509,1634,1739,1845,2187,2303,2477,2613,2762,2923,2987,3056,3148"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-pl_values-pl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1782", "endColumns": "139", "endOffsets": "1917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,254,369", "endColumns": "99,98,114,103", "endOffsets": "150,249,364,468"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3044,3144,3243,3358", "endColumns": "99,98,114,103", "endOffsets": "3139,3238,3353,3457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-pl\\values-pl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,792", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,451,565,670,3462", "endColumns": "96,101,97,98,113,104,121,100", "endOffsets": "147,249,347,446,560,665,787,3558"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,457,575,681,828,949,1056,1151,1318,1423,1594,1718,1873,2030,2095,2157", "endColumns": "99,163,117,105,146,120,106,94,166,104,170,123,154,156,64,61,79", "endOffsets": "292,456,574,680,827,948,1055,1150,1317,1422,1593,1717,1872,2029,2094,2156,2236"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,896,1064,1186,1296,1447,1572,1683,1922,2093,2202,2377,2505,2664,2825,2894,2960", "endColumns": "103,167,121,109,150,124,110,98,170,108,174,127,158,160,68,65,83", "endOffsets": "891,1059,1181,1291,1442,1567,1678,1777,2088,2197,2372,2500,2659,2820,2889,2955,3039"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-en-rXC_values-en-rXC.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,2294", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,2493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,255,454,665", "endColumns": "199,198,210,201", "endOffsets": "250,449,660,862"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "1482,1682,1881,2092", "endColumns": "199,198,210,201", "endOffsets": "1677,1876,2087,2289"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-en-rGB_values-en-rGB.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,3336", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,3432"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "133", "endOffsets": "332"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1756", "endColumns": "137", "endOffsets": "1889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2933,3031,3128,3237", "endColumns": "97,96,108,98", "endOffsets": "3026,3123,3232,3331"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-en-rGB\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,446,567,670,817,936,1048,1147,1302,1403,1551,1672,1814,1958,2017,2075", "endColumns": "100,147,120,102,146,118,111,98,154,100,147,120,141,143,58,57,74", "endOffsets": "297,445,566,669,816,935,1047,1146,1301,1402,1550,1671,1813,1957,2016,2074,2149"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,879,1031,1156,1263,1414,1537,1653,1894,2053,2158,2310,2435,2581,2729,2792,2854", "endColumns": "104,151,124,106,150,122,115,102,158,104,151,124,145,147,62,61,78", "endOffsets": "874,1026,1151,1258,1409,1532,1648,1751,2048,2153,2305,2430,2576,2724,2787,2849,2928"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-th_values-th.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,438,557,660,792,912,1027,1131,1271,1372,1515,1633,1769,1916,1976,2040", "endColumns": "101,142,118,102,131,119,114,103,139,100,142,117,135,146,59,63,79", "endOffsets": "294,437,556,659,791,911,1026,1130,1270,1371,1514,1632,1768,1915,1975,2039,2119"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "770,876,1023,1146,1253,1389,1513,1632,1869,2013,2118,2265,2387,2527,2678,2742,2810", "endColumns": "105,146,122,106,135,123,118,107,143,104,146,121,139,150,63,67,83", "endOffsets": "871,1018,1141,1248,1384,1508,1627,1735,2008,2113,2260,2382,2522,2673,2737,2805,2889"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-th\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1740", "endColumns": "128", "endOffsets": "1864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,770", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,866"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,352,450,553,658,3305", "endColumns": "95,102,97,97,102,104,111,100", "endOffsets": "146,249,347,445,548,653,765,3401"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-th\\values-th.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,368", "endColumns": "102,98,110,97", "endOffsets": "153,252,363,461"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2894,2997,3096,3207", "endColumns": "102,98,110,97", "endOffsets": "2992,3091,3202,3300"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-hr_values-hr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2968,3073,3173,3287", "endColumns": "104,99,113,101", "endOffsets": "3068,3168,3282,3384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-hr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,448,574,680,833,959,1070,1173,1319,1422,1575,1699,1842,1981,2045,2103", "endColumns": "101,152,125,105,152,125,110,102,145,102,152,123,142,138,63,57,76", "endOffsets": "294,447,573,679,832,958,1069,1172,1318,1421,1574,1698,1841,1980,2044,2102,2179"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "781,887,1044,1174,1284,1441,1571,1686,1925,2075,2182,2339,2467,2614,2757,2825,2887", "endColumns": "105,156,129,109,156,129,114,106,149,106,156,127,146,142,67,61,80", "endOffsets": "882,1039,1169,1279,1436,1566,1681,1788,2070,2177,2334,2462,2609,2752,2820,2882,2963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1793", "endColumns": "131", "endOffsets": "1920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,3389", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,3485"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-uk_values-uk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-uk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,579,685,835,960,1071,1172,1336,1438,1596,1717,1860,1998,2064,2121", "endColumns": "103,158,122,105,149,124,110,100,163,101,157,120,142,137,65,56,83", "endOffsets": "296,455,578,684,834,959,1070,1171,1335,1437,1595,1716,1859,1997,2063,2120,2204"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,890,1053,1180,1290,1444,1573,1688,1939,2107,2213,2375,2500,2647,2789,2859,2920", "endColumns": "107,162,126,109,153,128,114,104,167,105,161,124,146,141,69,60,87", "endOffsets": "885,1048,1175,1285,1439,1568,1683,1788,2102,2208,2370,2495,2642,2784,2854,2915,3003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,782", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,358,459,564,669,3453", "endColumns": "99,101,100,100,104,104,112,100", "endOffsets": "150,252,353,454,559,664,777,3549"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-uk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1793", "endColumns": "145", "endOffsets": "1934"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-uk\\values-uk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,392", "endColumns": "109,106,119,107", "endOffsets": "160,267,387,495"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3008,3118,3225,3345", "endColumns": "109,106,119,107", "endOffsets": "3113,3220,3340,3448"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-tr_values-tr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,768", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,864"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,449,551,657,3438", "endColumns": "96,101,97,96,101,105,110,100", "endOffsets": "147,249,347,444,546,652,763,3534"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1784", "endColumns": "146", "endOffsets": "1926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-tr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,450,582,683,826,952,1075,1177,1345,1448,1601,1731,1872,2035,2093,2153", "endColumns": "105,150,131,100,142,125,122,101,167,102,152,129,140,162,57,59,75", "endOffsets": "298,449,581,682,825,951,1074,1176,1344,1447,1600,1730,1871,2034,2092,2152,2228"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "768,878,1033,1169,1274,1421,1551,1678,1931,2103,2210,2367,2501,2646,2813,2875,2939", "endColumns": "109,154,135,104,146,129,126,105,171,106,156,133,144,166,61,63,79", "endOffsets": "873,1028,1164,1269,1416,1546,1673,1779,2098,2205,2362,2496,2641,2808,2870,2934,3014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-tr\\values-tr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,261,368", "endColumns": "99,105,106,105", "endOffsets": "150,256,363,469"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3019,3119,3225,3332", "endColumns": "99,105,106,105", "endOffsets": "3114,3220,3327,3433"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-zh-rCN_values-zh-rCN.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,419,530,628,729,841,939,1027,1131,1228,1354,1465,1565,1669,1721,1774", "endColumns": "96,124,110,97,100,111,97,87,103,96,125,110,99,103,51,52,69", "endOffsets": "293,418,529,627,728,840,938,1026,1130,1227,1353,1464,1564,1668,1720,1773,1843"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "719,820,949,1064,1166,1271,1387,1489,1680,1788,1889,2019,2134,2238,2346,2402,2459", "endColumns": "100,128,114,101,104,115,101,91,107,100,129,114,103,107,55,56,73", "endOffsets": "815,944,1059,1161,1266,1382,1484,1576,1783,1884,2014,2129,2233,2341,2397,2454,2528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,138,230,331", "endColumns": "82,91,100,92", "endOffsets": "133,225,326,419"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2533,2616,2708,2809", "endColumns": "82,91,100,92", "endOffsets": "2611,2703,2804,2897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-zh-rCN\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "94", "endOffsets": "293"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1581", "endColumns": "98", "endOffsets": "1675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,719", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,815"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,248,342,436,529,623,2902", "endColumns": "91,100,93,93,92,93,95,100", "endOffsets": "142,243,337,431,524,618,714,2998"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-et_values-et.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,3408", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,3504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1782", "endColumns": "139", "endOffsets": "1917"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,887,1049,1176,1284,1434,1563,1679,1922,2082,2190,2354,2486,2641,2786,2849,2914", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "882,1044,1171,1279,1429,1558,1674,1777,2077,2185,2349,2481,2636,2781,2844,2909,2997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3002,3102,3202,3309", "endColumns": "99,99,106,98", "endOffsets": "3097,3197,3304,3403"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-fa_values-fa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,251,362", "endColumns": "98,96,110,102", "endOffsets": "149,246,357,460"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3005,3104,3201,3312", "endColumns": "98,96,110,102", "endOffsets": "3099,3196,3307,3410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-fa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,450,575,674,810,932,1042,1140,1289,1395,1561,1688,1837,1989,2051,2115", "endColumns": "103,152,124,98,135,121,109,97,148,105,165,126,148,151,61,63,80", "endOffsets": "296,449,574,673,809,931,1041,1139,1288,1394,1560,1687,1836,1988,2050,2114,2195"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "779,887,1044,1173,1276,1416,1542,1656,1913,2066,2176,2346,2477,2630,2786,2852,2920", "endColumns": "107,156,128,102,139,125,113,101,152,109,169,130,152,155,65,67,84", "endOffsets": "882,1039,1168,1271,1411,1537,1651,1753,2061,2171,2341,2472,2625,2781,2847,2915,3000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-fa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "150", "endOffsets": "345"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1758", "endColumns": "154", "endOffsets": "1908"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-fa\\values-fa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,779", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,875"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,355,455,556,662,3415", "endColumns": "98,101,98,99,100,105,116,100", "endOffsets": "149,251,350,450,551,657,774,3511"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-nl_values-nl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,444,568,675,838,961,1080,1182,1356,1458,1623,1745,1904,2082,2146,2205", "endColumns": "103,146,123,106,162,122,118,101,173,101,164,121,158,177,63,58,74", "endOffsets": "296,443,567,674,837,960,1079,1181,1355,1457,1622,1744,1903,2081,2145,2204,2279"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "789,897,1048,1176,1287,1454,1581,1704,1953,2131,2237,2406,2532,2695,2877,2945,3008", "endColumns": "107,150,127,110,166,126,122,105,177,105,168,125,162,181,67,62,78", "endOffsets": "892,1043,1171,1282,1449,1576,1699,1805,2126,2232,2401,2527,2690,2872,2940,3003,3082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,259,370", "endColumns": "102,100,110,98", "endOffsets": "153,254,365,464"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3087,3190,3291,3402", "endColumns": "102,100,110,98", "endOffsets": "3185,3286,3397,3496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-nl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1810", "endColumns": "142", "endOffsets": "1948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-nl\\values-nl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,789", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,885"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,359,459,566,670,3501", "endColumns": "101,101,99,99,106,103,118,100", "endOffsets": "152,254,354,454,561,665,784,3597"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ms_values-ms.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ms\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,464,590,692,860,988,1104,1207,1388,1493,1664,1795,1962,2133,2196,2256", "endColumns": "101,168,125,101,167,127,115,102,180,104,170,130,166,170,62,59,78", "endOffsets": "294,463,589,691,859,987,1103,1206,1387,1492,1663,1794,1961,2132,2195,2255,2334"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "798,904,1077,1207,1313,1485,1617,1737,1990,2175,2284,2459,2594,2765,2940,3007,3071", "endColumns": "105,172,129,105,171,131,119,106,184,108,174,134,170,174,66,63,82", "endOffsets": "899,1072,1202,1308,1480,1612,1732,1839,2170,2279,2454,2589,2760,2935,3002,3066,3149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,379", "endColumns": "104,99,118,101", "endOffsets": "155,255,374,476"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3154,3259,3359,3478", "endColumns": "104,99,118,101", "endOffsets": "3254,3354,3473,3575"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ms\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1844", "endColumns": "145", "endOffsets": "1985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,3580", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,3676"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ro_values-ro.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,890,1050,1180,1290,1441,1571,1694,1947,2109,2220,2379,2512,2658,2824,2893,2961", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "885,1045,1175,1285,1436,1566,1689,1798,2104,2215,2374,2507,2653,2819,2888,2956,3039"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,3469", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,3565"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1803", "endColumns": "143", "endOffsets": "1942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,377", "endColumns": "106,101,112,102", "endOffsets": "157,259,372,475"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3044,3151,3253,3366", "endColumns": "106,101,112,102", "endOffsets": "3146,3248,3361,3464"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-be_values-be.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,3454", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,3550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-be\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,454,575,681,832,954,1065,1165,1323,1426,1585,1709,1858,2013,2078,2136", "endColumns": "102,157,120,105,150,121,110,99,157,102,158,123,148,154,64,57,74", "endOffsets": "295,453,574,680,831,953,1064,1164,1322,1425,1584,1708,1857,2012,2077,2135,2210"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,893,1055,1180,1290,1445,1571,1686,1936,2098,2205,2368,2496,2649,2808,2877,2939", "endColumns": "106,161,124,109,154,125,114,103,161,106,162,127,152,158,68,61,78", "endOffsets": "888,1050,1175,1285,1440,1566,1681,1785,2093,2200,2363,2491,2644,2803,2872,2934,3013"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-be\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1790", "endColumns": "145", "endOffsets": "1931"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,272,384", "endColumns": "108,107,111,106", "endOffsets": "159,267,379,486"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3018,3127,3235,3347", "endColumns": "108,107,111,106", "endOffsets": "3122,3230,3342,3449"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ta_values-ta.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,801", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,897"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,558,673,3467", "endColumns": "95,102,98,97,106,114,127,100", "endOffsets": "146,249,348,446,553,668,796,3563"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ta\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "156", "endOffsets": "351"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1785", "endColumns": "160", "endOffsets": "1941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ta\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,667,815,939,1048,1145,1321,1424,1573,1704,1854,2006,2064,2123", "endColumns": "100,147,122,101,147,123,108,96,175,102,148,130,149,151,57,58,76", "endOffsets": "293,441,564,666,814,938,1047,1144,1320,1423,1572,1703,1853,2005,2063,2122,2199"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "801,906,1058,1185,1291,1443,1571,1684,1946,2126,2233,2386,2521,2675,2831,2893,2956", "endColumns": "104,151,126,105,151,127,112,100,179,106,152,134,153,155,61,62,80", "endOffsets": "901,1053,1180,1286,1438,1566,1679,1780,2121,2228,2381,2516,2670,2826,2888,2951,3032"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ta\\values-ta.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,274,381", "endColumns": "116,101,106,103", "endOffsets": "167,269,376,480"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3037,3154,3256,3363", "endColumns": "116,101,106,103", "endOffsets": "3149,3251,3358,3462"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ur_values-ur.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,3435", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,3531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ur\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1768", "endColumns": "151", "endOffsets": "1915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,106", "endOffsets": "151,252,363,470"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3015,3116,3217,3328", "endColumns": "100,100,110,106", "endOffsets": "3111,3212,3323,3430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ur\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,457,579,686,822,945,1053,1153,1301,1407,1575,1699,1841,2006,2065,2128", "endColumns": "103,159,121,106,135,122,107,99,147,105,167,123,141,164,58,62,83", "endOffsets": "296,456,578,685,821,944,1052,1152,1300,1406,1574,1698,1840,2005,2064,2127,2211"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "776,884,1048,1174,1285,1425,1552,1664,1920,2072,2182,2354,2482,2628,2797,2860,2927", "endColumns": "107,163,125,110,139,126,111,103,151,109,171,127,145,168,62,66,87", "endOffsets": "879,1043,1169,1280,1420,1547,1659,1763,2067,2177,2349,2477,2623,2792,2855,2922,3010"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-sv_values-sv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "783,890,1047,1174,1284,1425,1550,1673,1925,2073,2181,2343,2471,2625,2781,2847,2910", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "885,1042,1169,1279,1420,1545,1668,1772,2068,2176,2338,2466,2620,2776,2842,2905,2984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,3400", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,3496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1777", "endColumns": "147", "endOffsets": "1920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2989,3089,3189,3302", "endColumns": "99,99,112,97", "endOffsets": "3084,3184,3297,3395"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-watch-v20_values-watch-v20.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3778c63841cf471867d49ac28a13e9e1\\transformed\\jetified-play-services-auth-21.0.0\\res\\values-watch-v20\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11", "startColumns": "0,0,0,0,0,0,0,0", "startOffsets": "173,248,324,399,474,561,638,725", "endColumns": "74,75,74,74,86,76,86,75", "endOffsets": "247,323,398,473,560,637,724,800"}, "to": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,134,214,293,372,463,544,635", "endColumns": "78,79,78,78,90,80,90,79", "endOffsets": "129,209,288,367,458,539,630,710"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce9588d11a1ef318240bbc000876080d\\transformed\\jetified-android-maps-utils-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "132,219,223,226", "startColumns": "4,4,4,4", "startOffsets": "6848,13574,13782,13942", "endLines": "132,222,225,230", "endColumns": "37,12,12,12", "endOffsets": "6881,13777,13937,14189"}}, {"source": "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,830", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "472,996"}, "to": {"startLines": "199,203", "startColumns": "4,4", "startOffsets": "12327,12508", "endLines": "202,205", "endColumns": "12,12", "endOffsets": "12503,12672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d42a15b305521b00ec0ead9d57f5e87\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "8039", "endColumns": "42", "endOffsets": "8077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "63,64,65,66,67,68,69,70,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,360,436", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2313,2403,2483,2573,2663,2743,2824,2904,9042,9147,9328,9453,9560,9740,9863,9979,10249,10437,10542,10723,10848,11023,11171,11234,11296,18631,20453", "endLines": "63,64,65,66,67,68,69,70,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,372,454", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2398,2478,2568,2658,2738,2819,2899,2979,9142,9323,9448,9555,9735,9858,9974,10077,10432,10537,10718,10843,11018,11166,11229,11291,11370,18941,20865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3943c3daa37d64d60b591e6ec78d8073\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "134,138,159,312,317", "startColumns": "4,4,4,4,4", "startOffsets": "6946,7115,8246,17463,17633", "endLines": "134,138,159,316,320", "endColumns": "56,64,63,24,24", "endOffsets": "6998,7175,8305,17628,17777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13c2522d2cb4fb3d641d53c468f44d4f\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "137,156", "startColumns": "4,4", "startOffsets": "7073,8082", "endColumns": "41,59", "endOffsets": "7110,8137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "57,58,59,60,73,74,188,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1933,1991,2057,2120,3116,3187,11375,11443,11510,11589", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "1986,2052,2115,2177,3182,3254,11438,11505,11584,11653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b9284c470170d74414f94dc98b89b8e\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,133,231,237,455,463,478", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,6886,14194,14389,20870,21152,21766", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,133,236,241,462,477,493", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,6941,14384,14542,21147,21761,22415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,61,62,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,135,136,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161,163,164,165,166,167,168,169,198,206,207,211,212,216,217,218,242,248,258,291,321,354", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,1773,1845,2182,2247,2984,3053,3259,3329,3397,3469,3539,3600,3674,3747,3808,3869,3931,3995,4057,4118,4186,4286,4346,4412,4485,4554,4611,4663,4725,4797,4873,4938,4997,5056,5116,5176,5236,5296,5356,5416,5476,5536,5596,5656,5715,5775,5835,5895,5955,6015,6075,6135,6195,6255,6315,6374,6434,6494,6553,6612,6671,6730,6789,7003,7038,7180,7235,7298,7353,7411,7469,7530,7593,7650,7701,7751,7812,7869,7935,7969,8004,8378,8531,8598,8670,8739,8808,8882,8954,12256,12677,12794,12995,13105,13306,13435,13507,14547,14750,15051,16782,17782,18464", "endLines": "25,55,56,61,62,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,135,136,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161,163,164,165,166,167,168,169,198,206,210,211,215,216,217,218,247,257,290,311,353,359", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,1840,1928,2242,2308,3048,3111,3324,3392,3464,3534,3595,3669,3742,3803,3864,3926,3990,4052,4113,4181,4281,4341,4407,4480,4549,4606,4658,4720,4792,4868,4933,4992,5051,5111,5171,5231,5291,5351,5411,5471,5531,5591,5651,5710,5770,5830,5890,5950,6010,6070,6130,6190,6250,6310,6369,6429,6489,6548,6607,6666,6725,6784,6843,7033,7068,7230,7293,7348,7406,7464,7525,7588,7645,7696,7746,7807,7864,7930,7964,7999,8034,8443,8593,8665,8734,8803,8877,8949,9037,12322,12789,12990,13100,13301,13430,13502,13569,14745,15046,16777,17458,18459,18626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f886222c74897d652e2c9506a518e149\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "8196", "endColumns": "49", "endOffsets": "8241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db90d39193f1db113b01ff3f74e10fce\\transformed\\jetified-play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "18946", "endLines": "435", "endColumns": "20", "endOffsets": "20448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\228692b75745c98c18fa52f377ee7ba9\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "157", "startColumns": "4", "startOffsets": "8142", "endColumns": "53", "endOffsets": "8191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8b5634de22240e81a4f4da6bf11fab58\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "162", "startColumns": "4", "startOffsets": "8448", "endColumns": "82", "endOffsets": "8526"}}, {"source": "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,577", "endColumns": "81,103,108,119,106,75", "endOffsets": "132,236,345,465,572,648"}, "to": {"startLines": "192,193,194,195,196,197", "startColumns": "4,4,4,4,4,4", "startOffsets": "11658,11740,11844,11953,12073,12180", "endColumns": "81,103,108,119,106,75", "endOffsets": "11735,11839,11948,12068,12175,12251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "160,178", "startColumns": "4,4", "startOffsets": "8310,10082", "endColumns": "67,166", "endOffsets": "8373,10244"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-my_values-my.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1825", "endColumns": "153", "endOffsets": "1974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "797,904,1068,1202,1313,1460,1592,1715,1979,2155,2261,2431,2574,2732,2919,2989,3062", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "899,1063,1197,1308,1455,1587,1710,1820,2150,2256,2426,2569,2727,2914,2984,3057,3146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,3588", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,3684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,106", "endOffsets": "159,265,380,487"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3151,3260,3366,3481", "endColumns": "108,105,114,106", "endOffsets": "3255,3361,3476,3583"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-el_values-el.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-el\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1859", "endColumns": "163", "endOffsets": "2018"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,272,397", "endColumns": "109,106,124,109", "endOffsets": "160,267,392,502"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3156,3266,3373,3498", "endColumns": "109,106,124,109", "endOffsets": "3261,3368,3493,3603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-el\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,482,610,717,894,1015,1129,1230,1415,1519,1685,1810,1984,2125,2190,2248", "endColumns": "106,181,127,106,176,120,113,100,184,103,165,124,173,140,64,57,78", "endOffsets": "299,481,609,716,893,1014,1128,1229,1414,1518,1684,1809,1983,2124,2189,2247,2326"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,901,1087,1219,1330,1511,1636,1754,2023,2212,2320,2490,2619,2797,2942,3011,3073", "endColumns": "110,185,131,110,180,124,117,104,188,107,169,128,177,144,68,61,82", "endOffsets": "896,1082,1214,1325,1506,1631,1749,1854,2207,2315,2485,2614,2792,2937,3006,3068,3151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-el\\values-el.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,790", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,567,673,3608", "endColumns": "97,102,99,102,107,105,116,100", "endOffsets": "148,251,351,454,562,668,785,3704"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-eu_values-eu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-eu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1804", "endColumns": "142", "endOffsets": "1942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,257,370", "endColumns": "99,101,112,104", "endOffsets": "150,252,365,470"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3061,3161,3263,3376", "endColumns": "99,101,112,104", "endOffsets": "3156,3258,3371,3476"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-eu\\values-eu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,786", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,356,459,564,667,3481", "endColumns": "97,102,99,102,104,102,118,100", "endOffsets": "148,251,351,454,559,662,781,3577"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-eu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,461,581,686,834,955,1075,1179,1353,1457,1616,1740,1890,2046,2108,2169", "endColumns": "99,167,119,104,147,120,119,103,173,103,158,123,149,155,61,60,87", "endOffsets": "292,460,580,685,833,954,1074,1178,1352,1456,1615,1739,1889,2045,2107,2168,2256"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,890,1062,1186,1295,1447,1572,1696,1947,2125,2233,2396,2524,2678,2838,2904,2969", "endColumns": "103,171,123,108,151,124,123,107,177,107,162,127,153,159,65,64,91", "endOffsets": "885,1057,1181,1290,1442,1567,1691,1799,2120,2228,2391,2519,2673,2833,2899,2964,3056"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-mn_values-mn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,790", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,454,559,671,3427", "endColumns": "97,101,100,97,104,111,118,100", "endOffsets": "148,250,351,449,554,666,785,3523"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-mn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,452,580,683,816,938,1063,1169,1309,1412,1576,1701,1838,2002,2059,2117", "endColumns": "105,152,127,102,132,121,124,105,139,102,163,124,136,163,56,57,72", "endOffsets": "298,451,579,682,815,937,1062,1168,1308,1411,1575,1700,1837,2001,2058,2116,2189"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,900,1057,1189,1296,1433,1559,1688,1948,2092,2199,2367,2496,2637,2805,2866,2928", "endColumns": "109,156,131,106,136,125,128,109,143,106,167,128,140,167,60,61,76", "endOffsets": "895,1052,1184,1291,1428,1554,1683,1793,2087,2194,2362,2491,2632,2800,2861,2923,3000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-mn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "145", "endOffsets": "340"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1798", "endColumns": "149", "endOffsets": "1943"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-mn\\values-mn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,264,369", "endColumns": "104,103,104,107", "endOffsets": "155,259,364,472"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3005,3110,3214,3319", "endColumns": "104,103,104,107", "endOffsets": "3105,3209,3314,3422"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-bg_values-bg.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,386", "endColumns": "110,107,111,109", "endOffsets": "161,269,381,491"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3103,3214,3322,3434", "endColumns": "110,107,111,109", "endOffsets": "3209,3317,3429,3539"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-bg\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1846", "endColumns": "137", "endOffsets": "1979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-bg\\values-bg.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,796", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,892"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,262,364,465,572,677,3544", "endColumns": "96,109,101,100,106,104,118,100", "endOffsets": "147,257,359,460,567,672,791,3640"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-bg\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,459,590,697,860,991,1106,1211,1376,1484,1655,1789,1942,2104,2170,2225", "endColumns": "104,160,130,106,162,130,114,104,164,107,170,133,152,161,65,54,68", "endOffsets": "297,458,589,696,859,990,1105,1210,1375,1483,1654,1788,1941,2103,2169,2224,2293"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "796,905,1070,1205,1316,1483,1618,1737,1984,2153,2265,2440,2578,2735,2901,2971,3030", "endColumns": "108,164,134,110,166,134,118,108,168,111,174,137,156,165,69,58,72", "endOffsets": "900,1065,1200,1311,1478,1613,1732,1841,2148,2260,2435,2573,2730,2896,2966,3025,3098"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-de_values-de.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,786", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,455,563,668,3553", "endColumns": "97,101,99,99,107,104,117,100", "endOffsets": "148,250,350,450,558,663,781,3649"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-de\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "140", "endOffsets": "335"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1848", "endColumns": "144", "endOffsets": "1988"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-de\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,458,582,690,864,991,1108,1223,1399,1507,1672,1799,1957,2129,2196,2255", "endColumns": "104,159,123,107,173,126,116,114,175,107,164,126,157,171,66,58,75", "endOffsets": "297,457,581,689,863,990,1107,1222,1398,1506,1671,1798,1956,2128,2195,2254,2330"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,895,1059,1187,1299,1477,1608,1729,1993,2173,2285,2454,2585,2747,2923,2994,3057", "endColumns": "108,163,127,111,177,130,120,118,179,111,168,130,161,175,70,62,79", "endOffsets": "890,1054,1182,1294,1472,1603,1724,1843,2168,2280,2449,2580,2742,2918,2989,3052,3132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-de\\values-de.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,371", "endColumns": "103,100,110,99", "endOffsets": "154,255,366,466"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3137,3241,3342,3453", "endColumns": "103,100,110,99", "endOffsets": "3236,3337,3448,3548"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ne_values-ne.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,268,382", "endColumns": "100,111,113,107", "endOffsets": "151,263,377,485"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3102,3203,3315,3429", "endColumns": "100,111,113,107", "endOffsets": "3198,3310,3424,3532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1817", "endColumns": "163", "endOffsets": "1976"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ne\\values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,3537", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,3633"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ne\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,454,584,697,864,996,1102,1203,1379,1489,1649,1778,1922,2070,2132,2200", "endColumns": "106,153,129,112,166,131,105,100,175,109,159,128,143,147,61,67,87", "endOffsets": "299,453,583,696,863,995,1101,1202,1378,1488,1648,1777,1921,2069,2131,2199,2287"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,886,1044,1178,1295,1466,1602,1712,1981,2161,2275,2439,2572,2720,2872,2938,3010", "endColumns": "110,157,133,116,170,135,109,104,179,113,163,132,147,151,65,71,91", "endOffsets": "881,1039,1173,1290,1461,1597,1707,1812,2156,2270,2434,2567,2715,2867,2933,3005,3097"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-kk_values-kk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,441,563,665,801,923,1042,1147,1308,1410,1563,1688,1837,1990,2049,2104", "endColumns": "98,148,121,101,135,121,118,104,160,101,152,124,148,152,58,54,73", "endOffsets": "291,440,562,664,800,922,1041,1146,1307,1409,1562,1687,1836,1989,2048,2103,2177"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "769,872,1025,1151,1257,1397,1523,1646,1919,2084,2190,2347,2476,2629,2786,2849,2908", "endColumns": "102,152,125,105,139,125,122,108,164,105,156,128,152,156,62,58,77", "endOffsets": "867,1020,1146,1252,1392,1518,1641,1750,2079,2185,2342,2471,2624,2781,2844,2903,2981"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,259,367", "endColumns": "99,103,107,104", "endOffsets": "150,254,362,467"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2986,3086,3190,3298", "endColumns": "99,103,107,104", "endOffsets": "3081,3185,3293,3398"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-kk\\values-kk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,769", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,865"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,354,457,561,658,3403", "endColumns": "94,101,101,102,103,96,110,100", "endOffsets": "145,247,349,452,556,653,764,3499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-kk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "159", "endOffsets": "354"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1755", "endColumns": "163", "endOffsets": "1914"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-da_values-da.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,3399", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,3495"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1777", "endColumns": "126", "endOffsets": "1899"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2984,3096,3195,3302", "endColumns": "111,98,106,96", "endOffsets": "3091,3190,3297,3394"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,888,1048,1175,1284,1427,1552,1672,1904,2060,2166,2328,2455,2600,2778,2844,2906", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "883,1043,1170,1279,1422,1547,1667,1772,2055,2161,2323,2450,2595,2773,2839,2901,2979"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-pa_values-pa.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pa\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "146", "endOffsets": "341"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1799", "endColumns": "150", "endOffsets": "1945"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3053,3158,3259,3373", "endColumns": "104,100,113,102", "endOffsets": "3153,3254,3368,3471"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pa\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,591,696,839,964,1073,1172,1330,1435,1604,1732,1881,2038,2099,2161", "endColumns": "102,168,125,104,142,124,108,98,157,104,168,127,148,156,60,61,77", "endOffsets": "295,464,590,695,838,963,1072,1171,1329,1434,1603,1731,1880,2037,2098,2160,2238"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "788,895,1068,1198,1307,1454,1583,1696,1950,2112,2221,2394,2526,2679,2840,2905,2971", "endColumns": "106,172,129,108,146,128,112,102,161,108,172,131,152,160,64,65,81", "endOffsets": "890,1063,1193,1302,1449,1578,1691,1794,2107,2216,2389,2521,2674,2835,2900,2966,3048"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,3476", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,3572"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-vi_values-vi.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,785", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,881"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,556,669,3472", "endColumns": "96,101,98,99,102,112,115,100", "endOffsets": "147,249,348,448,551,664,780,3568"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-vi\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,449,570,675,836,962,1077,1177,1346,1449,1602,1728,1883,2028,2092,2152", "endColumns": "97,157,120,104,160,125,114,99,168,102,152,125,154,144,63,59,78", "endOffsets": "290,448,569,674,835,961,1076,1176,1345,1448,1601,1727,1882,2027,2091,2151,2230"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "785,887,1049,1174,1283,1448,1578,1697,1929,2102,2209,2366,2496,2655,2804,2872,2936", "endColumns": "101,161,124,108,164,129,118,103,172,106,156,129,158,148,67,63,82", "endOffsets": "882,1044,1169,1278,1443,1573,1692,1796,2097,2204,2361,2491,2650,2799,2867,2931,3014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-vi\\values-vi.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,172,283,397", "endColumns": "116,110,113,110", "endOffsets": "167,278,392,503"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3019,3136,3247,3361", "endColumns": "116,110,113,110", "endOffsets": "3131,3242,3356,3467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-vi\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "123", "endOffsets": "318"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1801", "endColumns": "127", "endOffsets": "1924"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-gu_values-gu.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-gu\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "142", "endOffsets": "337"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1755", "endColumns": "146", "endOffsets": "1897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-gu\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,459,580,688,822,940,1047,1143,1287,1391,1551,1672,1811,1957,2014,2076", "endColumns": "103,161,120,107,133,117,106,95,143,103,159,120,138,145,56,61,77", "endOffsets": "296,458,579,687,821,939,1046,1142,1286,1390,1550,1671,1810,1956,2013,2075,2153"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "773,881,1047,1172,1284,1422,1544,1655,1902,2050,2158,2322,2447,2590,2740,2801,2867", "endColumns": "107,165,124,111,137,121,110,99,147,107,163,124,142,149,60,65,81", "endOffsets": "876,1042,1167,1279,1417,1539,1650,1750,2045,2153,2317,2442,2585,2735,2796,2862,2944"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,366", "endColumns": "100,100,108,100", "endOffsets": "151,252,361,462"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2949,3050,3151,3260", "endColumns": "100,100,108,100", "endOffsets": "3045,3146,3255,3356"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,3361", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,3457"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-it_values-it.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3024,3124,3222,3332", "endColumns": "99,97,109,102", "endOffsets": "3119,3217,3327,3430"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1801", "endColumns": "135", "endOffsets": "1932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,3435", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,3531"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "802,907,1058,1183,1291,1449,1577,1697,1937,2094,2201,2355,2482,2638,2819,2886,2947", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "902,1053,1178,1286,1444,1572,1692,1796,2089,2196,2350,2477,2633,2814,2881,2942,3019"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-bn_values-bn.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,784", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,880"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,358,461,562,664,3374", "endColumns": "98,101,101,102,100,101,119,100", "endOffsets": "149,251,353,456,557,659,779,3470"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-bn\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,298,453,577,684,816,934,1042,1142,1281,1386,1538,1662,1791,1935,1991,2054", "endColumns": "104,154,123,106,131,117,107,99,138,104,151,123,128,143,55,62,85", "endOffsets": "297,452,576,683,815,933,1041,1141,1280,1385,1537,1661,1790,1934,1990,2053,2139"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "784,893,1052,1180,1291,1427,1549,1661,1917,2060,2169,2325,2453,2586,2734,2794,2861", "endColumns": "108,158,127,110,135,121,111,103,142,108,155,127,132,147,59,66,89", "endOffsets": "888,1047,1175,1286,1422,1544,1656,1760,2055,2164,2320,2448,2581,2729,2789,2856,2946"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-bn\\values-bn.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,263,372", "endColumns": "105,101,108,105", "endOffsets": "156,258,367,473"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2951,3057,3159,3268", "endColumns": "105,101,108,105", "endOffsets": "3052,3154,3263,3369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-bn\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "147", "endOffsets": "342"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1765", "endColumns": "151", "endOffsets": "1912"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-sk_values-sk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,265,380", "endColumns": "106,102,114,101", "endOffsets": "157,260,375,477"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3059,3166,3269,3384", "endColumns": "106,102,114,101", "endOffsets": "3161,3264,3379,3481"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1791", "endColumns": "138", "endOffsets": "1925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sk\\values-sk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,792", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,354,452,562,670,3486", "endColumns": "95,101,100,97,109,107,121,100", "endOffsets": "146,248,349,447,557,665,787,3582"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,571,677,829,953,1062,1160,1325,1432,1598,1724,1883,2043,2107,2170", "endColumns": "101,155,119,105,151,123,108,97,164,106,165,125,158,159,63,62,82", "endOffsets": "294,450,570,676,828,952,1061,1159,1324,1431,1597,1723,1882,2042,2106,2169,2252"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,898,1058,1182,1292,1448,1576,1689,1930,2099,2210,2380,2510,2673,2837,2905,2972", "endColumns": "105,159,123,109,155,127,112,101,168,110,169,129,162,163,67,66,86", "endOffsets": "893,1053,1177,1287,1443,1571,1684,1786,2094,2205,2375,2505,2668,2832,2900,2967,3054"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-sq_values-sq.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,3561", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,3657"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3133,3248,3349,3460", "endColumns": "114,100,110,100", "endOffsets": "3243,3344,3455,3556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1853", "endColumns": "128", "endOffsets": "1977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "792,899,1072,1209,1316,1477,1611,1737,1982,2152,2260,2435,2573,2735,2919,2984,3051", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "894,1067,1204,1311,1472,1606,1732,1848,2147,2255,2430,2568,2730,2914,2979,3046,3128"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-pt-rBR_values-pt-rBR.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,3484", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,3580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,895,1043,1170,1278,1445,1575,1697,1947,2117,2225,2389,2519,2676,2833,2902,2968", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "890,1038,1165,1273,1440,1570,1692,1797,2112,2220,2384,2514,2671,2828,2897,2963,3047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1802", "endColumns": "144", "endOffsets": "1942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3052,3167,3266,3378", "endColumns": "114,98,111,105", "endOffsets": "3162,3261,3373,3479"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-lv_values-lv.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1791", "endColumns": "165", "endOffsets": "1952"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,893,1054,1187,1295,1437,1568,1685,1957,2132,2241,2410,2545,2714,2869,2933,3001", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "888,1049,1182,1290,1432,1563,1680,1786,2127,2236,2405,2540,2709,2864,2928,2996,3085"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3090,3193,3292,3408", "endColumns": "102,98,115,101", "endOffsets": "3188,3287,3403,3505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,3510", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,3606"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-fr-rCA_values-fr-rCA.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,383", "endColumns": "106,101,118,104", "endOffsets": "157,259,378,483"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3193,3300,3402,3521", "endColumns": "106,101,118,104", "endOffsets": "3295,3397,3516,3621"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "156", "endOffsets": "355"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1841", "endColumns": "160", "endOffsets": "1997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-fr-rCA\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,299,475,601,706,873,1002,1119,1228,1419,1527,1708,1840,1996,2171,2240,2303", "endColumns": "101,175,125,104,166,128,116,108,190,107,180,131,155,174,68,62,79", "endOffsets": "298,474,600,705,872,1001,1118,1227,1418,1526,1707,1839,1995,2170,2239,2302,2382"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "778,884,1064,1194,1303,1474,1607,1728,2002,2197,2309,2494,2630,2790,2969,3042,3109", "endColumns": "105,179,129,108,170,132,120,112,194,111,184,135,159,178,72,66,83", "endOffsets": "879,1059,1189,1298,1469,1602,1723,1836,2192,2304,2489,2625,2785,2964,3037,3104,3188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-fr-rCA\\values-fr-rCA.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,778", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,874"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,3626", "endColumns": "97,101,98,101,103,103,113,100", "endOffsets": "148,250,349,451,555,659,773,3722"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-gl_values-gl.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,376", "endColumns": "106,101,111,105", "endOffsets": "157,259,371,477"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3064,3171,3273,3385", "endColumns": "106,101,111,105", "endOffsets": "3166,3268,3380,3486"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-gl\\values-gl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,783", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,3491", "endColumns": "98,101,99,97,106,105,115,100", "endOffsets": "149,251,351,449,556,662,778,3587"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-gl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,454,579,683,838,966,1081,1186,1353,1458,1623,1754,1915,2063,2126,2191", "endColumns": "101,158,124,103,154,127,114,104,166,104,164,130,160,147,62,64,80", "endOffsets": "294,453,578,682,837,965,1080,1185,1352,1457,1622,1753,1914,2062,2125,2190,2271"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "783,889,1052,1181,1289,1448,1580,1699,1942,2113,2222,2391,2526,2691,2843,2910,2979", "endColumns": "105,162,128,107,158,131,118,108,170,108,168,134,164,151,66,68,84", "endOffsets": "884,1047,1176,1284,1443,1575,1694,1803,2108,2217,2386,2521,2686,2838,2905,2974,3059"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-gl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "129", "endOffsets": "324"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1808", "endColumns": "133", "endOffsets": "1937"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-te_values-te.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,3436", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,3532"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "133", "endOffsets": "328"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1783", "endColumns": "137", "endOffsets": "1916"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,274,385", "endColumns": "110,107,110,106", "endOffsets": "161,269,380,487"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2999,3110,3218,3329", "endColumns": "110,107,110,106", "endOffsets": "3105,3213,3324,3431"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,301,451,577,688,821,942,1043,1139,1284,1392,1541,1669,1816,1975,2035,2101", "endColumns": "107,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,79", "endOffsets": "300,450,576,687,820,941,1042,1138,1283,1391,1540,1668,1815,1974,2034,2100,2180"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,917,1071,1201,1316,1453,1578,1683,1921,2070,2182,2335,2467,2618,2781,2845,2915", "endColumns": "111,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,83", "endOffsets": "912,1066,1196,1311,1448,1573,1678,1778,2065,2177,2330,2462,2613,2776,2840,2910,2994"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-is_values-is.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,3332", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,3428"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "772,878,1032,1157,1266,1407,1532,1641,1875,2029,2135,2292,2418,2560,2714,2778,2841", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "873,1027,1152,1261,1402,1527,1636,1741,2024,2130,2287,2413,2555,2709,2773,2836,2915"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1746", "endColumns": "128", "endOffsets": "1870"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2920,3024,3125,3231", "endColumns": "103,100,105,100", "endOffsets": "3019,3120,3226,3327"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-mk_values-mk.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,774", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,450,555,658,3451", "endColumns": "97,101,96,97,104,102,115,100", "endOffsets": "148,250,347,445,550,653,769,3547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-mk\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,688,829,958,1074,1180,1333,1436,1598,1727,1876,2031,2096,2156", "endColumns": "102,156,128,105,140,128,115,105,152,102,161,128,148,154,64,59,74", "endOffsets": "295,452,581,687,828,957,1073,1179,1332,1435,1597,1726,1875,2030,2095,2155,2230"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "774,881,1042,1175,1285,1430,1563,1683,1930,2087,2194,2360,2493,2646,2805,2874,2938", "endColumns": "106,160,132,109,144,132,119,109,156,106,165,132,152,158,68,63,78", "endOffsets": "876,1037,1170,1280,1425,1558,1678,1788,2082,2189,2355,2488,2641,2800,2869,2933,3012"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-mk\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "132", "endOffsets": "327"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1793", "endColumns": "136", "endOffsets": "1925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-mk\\values-mk.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,273,388", "endColumns": "112,104,114,100", "endOffsets": "163,268,383,484"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3017,3130,3235,3350", "endColumns": "112,104,114,100", "endOffsets": "3125,3230,3345,3446"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-km_values-km.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2997,3094,3191,3324", "endColumns": "96,96,132,99", "endOffsets": "3089,3186,3319,3419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-km\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "144", "endOffsets": "339"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1775", "endColumns": "148", "endOffsets": "1919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,3424", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,3520"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-km\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,442,559,660,818,938,1055,1160,1314,1427,1594,1715,1856,2010,2070,2124", "endColumns": "97,150,116,100,157,119,116,104,153,112,166,120,140,153,59,53,72", "endOffsets": "290,441,558,659,817,937,1054,1159,1313,1426,1593,1714,1855,2009,2069,2123,2196"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "776,878,1033,1154,1259,1421,1545,1666,1924,2082,2199,2370,2495,2640,2798,2862,2920", "endColumns": "101,154,120,104,161,123,120,108,157,116,170,124,144,157,63,57,76", "endOffsets": "873,1028,1149,1254,1416,1540,1661,1770,2077,2194,2365,2490,2635,2793,2857,2915,2992"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ky_values-ky.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,786", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,882"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,257,360,467,571,675,3409", "endColumns": "99,101,102,106,103,103,110,100", "endOffsets": "150,252,355,462,566,670,781,3505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ky\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,440,565,668,809,932,1043,1148,1313,1416,1562,1688,1821,1981,2041,2097", "endColumns": "101,144,124,102,140,122,110,104,164,102,145,125,132,159,59,55,73", "endOffsets": "294,439,564,667,808,931,1042,1147,1312,1415,1561,1687,1820,1980,2040,2096,2170"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "786,892,1041,1170,1277,1422,1549,1664,1931,2100,2207,2357,2487,2624,2788,2852,2912", "endColumns": "105,148,128,106,144,126,114,108,168,106,149,129,136,163,63,59,77", "endOffsets": "887,1036,1165,1272,1417,1544,1659,1768,2095,2202,2352,2482,2619,2783,2847,2907,2985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ky\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "153", "endOffsets": "348"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1773", "endColumns": "157", "endOffsets": "1926"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ky\\values-ky.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,154,259,368", "endColumns": "98,104,108,105", "endOffsets": "149,254,363,469"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2990,3089,3194,3303", "endColumns": "98,104,108,105", "endOffsets": "3084,3189,3298,3404"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-si_values-si.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,270,386", "endColumns": "107,106,115,104", "endOffsets": "158,265,381,486"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2977,3085,3192,3308", "endColumns": "107,106,115,104", "endOffsets": "3080,3187,3303,3408"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-si\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "134", "endOffsets": "329"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1766", "endColumns": "138", "endOffsets": "1900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-si\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,447,567,676,814,934,1046,1140,1287,1398,1550,1677,1817,1974,2043,2100", "endColumns": "103,149,119,108,137,119,111,93,146,110,151,126,139,156,68,56,75", "endOffsets": "296,446,566,675,813,933,1045,1139,1286,1397,1549,1676,1816,1973,2042,2099,2175"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,895,1049,1173,1286,1428,1552,1668,1905,2056,2171,2327,2458,2602,2763,2836,2897", "endColumns": "107,153,123,112,141,123,115,97,150,114,155,130,143,160,72,60,79", "endOffsets": "890,1044,1168,1281,1423,1547,1663,1761,2051,2166,2322,2453,2597,2758,2831,2892,2972"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-si\\values-si.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,787", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,260,365,470,569,673,3413", "endColumns": "101,102,104,104,98,103,113,100", "endOffsets": "152,255,360,465,564,668,782,3509"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-mr_values-mr.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,787", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,259,360,463,565,670,3361", "endColumns": "99,103,100,102,101,104,116,100", "endOffsets": "150,254,355,458,560,665,782,3457"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-mr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1768", "endColumns": "142", "endOffsets": "1906"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-mr\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,460,579,687,828,945,1049,1142,1288,1392,1542,1662,1797,1946,2002,2064", "endColumns": "102,163,118,107,140,116,103,92,145,103,149,119,134,148,55,61,76", "endOffsets": "295,459,578,686,827,944,1048,1141,1287,1391,1541,1661,1796,1945,2001,2063,2140"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,894,1062,1185,1297,1442,1563,1671,1911,2061,2169,2323,2447,2586,2739,2799,2865", "endColumns": "106,167,122,111,144,120,107,96,149,107,153,123,138,152,59,65,80", "endOffsets": "889,1057,1180,1292,1437,1558,1666,1763,2056,2164,2318,2442,2581,2734,2794,2860,2941"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-mr\\values-mr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,156,257,368", "endColumns": "100,100,110,101", "endOffsets": "151,252,363,465"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2946,3047,3148,3259", "endColumns": "100,100,110,101", "endOffsets": "3042,3143,3254,3356"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-or_values-or.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1825", "endColumns": "139", "endOffsets": "1960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3070,3180,3285,3398", "endColumns": "109,104,112,108", "endOffsets": "3175,3280,3393,3502"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,3507", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,3603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,901,1062,1194,1311,1466,1601,1715,1965,2132,2245,2406,2539,2689,2846,2911,2983", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "896,1057,1189,1306,1461,1596,1710,1820,2127,2240,2401,2534,2684,2841,2906,2978,3065"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-es_values-es.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,263,378", "endColumns": "106,100,114,104", "endOffsets": "157,258,373,478"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3124,3231,3332,3447", "endColumns": "106,100,114,104", "endOffsets": "3226,3327,3442,3547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-es\\values-es.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,3552", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,3648"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,456,583,687,844,973,1091,1197,1385,1490,1651,1779,1940,2093,2156,2221", "endColumns": "103,158,126,103,156,128,117,105,187,104,160,127,160,152,62,64,80", "endOffsets": "296,455,582,686,843,972,1090,1196,1384,1489,1650,1778,1939,2092,2155,2220,2301"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,895,1058,1189,1297,1458,1591,1713,1983,2175,2284,2449,2581,2746,2903,2970,3039", "endColumns": "107,162,130,107,160,132,121,109,191,108,164,131,164,156,66,68,84", "endOffsets": "890,1053,1184,1292,1453,1586,1708,1818,2170,2279,2444,2576,2741,2898,2965,3034,3119"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-es\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "155", "endOffsets": "350"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1823", "endColumns": "159", "endOffsets": "1978"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-am_values-am.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "742,844,983,1105,1207,1334,1457,1565,1799,1927,2030,2175,2298,2433,2560,2620,2677", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "839,978,1100,1202,1329,1452,1560,1662,1922,2025,2170,2293,2428,2555,2615,2672,2743"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1667", "endColumns": "131", "endOffsets": "1794"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,3142", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,3238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2748,2844,2939,3045", "endColumns": "95,94,105,96", "endOffsets": "2839,2934,3040,3137"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-lo_values-lo.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1777", "endColumns": "131", "endOffsets": "1904"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,3426", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,3522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-lo\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,463,589,692,840,961,1065,1177,1325,1426,1588,1713,1880,2037,2101,2166", "endColumns": "103,165,125,102,147,120,103,111,147,100,161,124,166,156,63,64,80", "endOffsets": "296,462,588,691,839,960,1064,1176,1324,1425,1587,1712,1879,2036,2100,2165,2246"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "761,869,1039,1169,1276,1428,1553,1661,1909,2061,2166,2332,2461,2632,2793,2861,2930", "endColumns": "107,169,129,106,151,124,107,115,151,104,165,128,170,160,67,68,84", "endOffsets": "864,1034,1164,1271,1423,1548,1656,1772,2056,2161,2327,2456,2627,2788,2856,2925,3010"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,251,366", "endColumns": "97,97,114,99", "endOffsets": "148,246,361,461"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3015,3113,3211,3326", "endColumns": "97,97,114,99", "endOffsets": "3108,3206,3321,3421"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-night-v8_values-night-v8.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\android\\app\\src\\main\\res\\values-night\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "175,829", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "471,995"}, "to": {"startLines": "2,6", "startColumns": "4,4", "startOffsets": "55,236", "endLines": "5,8", "endColumns": "12,12", "endOffsets": "231,400"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ru_values-ru.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,3485", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,3581"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "782,889,1055,1181,1291,1433,1562,1677,1938,2119,2226,2389,2515,2682,2840,2909,2969", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "884,1050,1176,1286,1428,1557,1672,1776,2114,2221,2384,2510,2677,2835,2904,2964,3050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3055,3163,3268,3380", "endColumns": "107,104,111,104", "endOffsets": "3158,3263,3375,3480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1781", "endColumns": "156", "endOffsets": "1933"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-af_values-af.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,3403", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,3499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-af\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "138", "endOffsets": "333"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1781", "endColumns": "142", "endOffsets": "1919"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-af\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,448,570,676,822,940,1057,1155,1317,1421,1574,1697,1832,1982,2044,2103", "endColumns": "102,151,121,105,145,117,116,97,161,103,152,122,134,149,61,58,74", "endOffsets": "295,447,569,675,821,939,1056,1154,1316,1420,1573,1696,1831,1981,2043,2102,2177"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,894,1050,1176,1286,1436,1558,1679,1924,2090,2198,2355,2482,2621,2775,2841,2904", "endColumns": "106,155,125,109,149,121,120,101,165,107,156,126,138,153,65,62,78", "endOffsets": "889,1045,1171,1281,1431,1553,1674,1776,2085,2193,2350,2477,2616,2770,2836,2899,2978"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,262,375", "endColumns": "104,101,112,99", "endOffsets": "155,257,370,470"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2983,3088,3190,3303", "endColumns": "104,101,112,99", "endOffsets": "3083,3185,3298,3398"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-lt_values-lt.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,3502", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,3598"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "806,913,1065,1197,1304,1457,1587,1706,1971,2137,2246,2411,2545,2698,2848,2916,2982", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "908,1060,1192,1299,1452,1582,1701,1807,2132,2241,2406,2540,2693,2843,2911,2977,3067"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3072,3177,3282,3396", "endColumns": "104,104,113,105", "endOffsets": "3172,3277,3391,3497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1812", "endColumns": "158", "endOffsets": "1966"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ar_values-ar.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1721", "endColumns": "129", "endOffsets": "1846"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2863,2963,3061,3169", "endColumns": "99,97,107,101", "endOffsets": "2958,3056,3164,3266"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,3271", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,3367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "767,871,1015,1137,1242,1380,1508,1619,1851,1988,2092,2242,2364,2503,2649,2713,2779", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "866,1010,1132,1237,1375,1503,1614,1716,1983,2087,2237,2359,2498,2644,2708,2774,2858"}}]}, {"outputFile": "com.gribyassine.getdoctor.app-merged_res-33:/values-ko_values-ko.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2679,2765,2858,2961", "endColumns": "85,92,102,93", "endOffsets": "2760,2853,2956,3050"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,3055", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,3151"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-ko\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,428,540,638,749,862,965,1056,1197,1296,1428,1542,1656,1771,1826,1880", "endColumns": "99,134,111,97,110,112,102,90,140,98,131,113,113,114,54,53,70", "endOffsets": "292,427,539,637,748,861,964,1055,1196,1295,1427,1541,1655,1770,1825,1879,1950"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "732,836,975,1091,1193,1308,1425,1532,1748,1893,1996,2132,2250,2368,2487,2546,2604", "endColumns": "103,138,115,101,114,116,106,94,144,102,135,117,117,118,58,57,74", "endOffsets": "831,970,1086,1188,1303,1420,1527,1622,1888,1991,2127,2245,2363,2482,2541,2599,2674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-ko\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "116", "endOffsets": "311"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1627", "endColumns": "120", "endOffsets": "1743"}}]}]}
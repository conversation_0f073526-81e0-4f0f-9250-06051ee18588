[{"level_": 0, "message_": "Start JSON generation. Platform version: 33 min SDK version: x86", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON 'C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\build\\.cxx\\debug\\624rs4c5\\x86\\android_gradle_build.json' was up-to-date", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}, {"level_": 0, "message_": "JSON generation completed without problems", "file_": "C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt", "tag_": "debug|x86", "diagnosticCode_": 0, "memoizedIsInitialized": 1, "unknownFields": {"fields": {}}, "memoizedSize": -1, "memoizedHashCode": 0}]
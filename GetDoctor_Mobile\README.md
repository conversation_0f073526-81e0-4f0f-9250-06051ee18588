# GetDoctor - Complete Healthcare Management System

<h2>:blush: :blush: :blush:  https://www.izi.health - IZI Health is an advanced version the getdoctor. Please have a look.</h2>

## Overview

GetDoctor is a comprehensive healthcare management system built with modern technologies including Flutter for mobile, Firebase for backend services, and Blazor with .NET Core API and SQL Server for web applications. This complete package provides an online doctor appointment and scheduling system with an intuitive interface for doctors, patients, assistants, and staff.

## 🚀 Tech Stack

### Mobile App (Flutter)
- **Framework**: Flutter 3.x with Dart
- **State Management**: BLoC Pattern with Cubits
- **Architecture**: Clean Architecture (Domain, Data, Presentation layers)
- **Backend**: Firebase (Authentication, Firestore, Cloud Functions)
- **Maps**: Google Maps integration
- **Location Services**: Geolocator and Location plugins
- **Dependency Injection**: GetIt service locator
- **Build System**: Android Gradle Plugin 8.2.2

### Web Application
- **Frontend**: Blazor Server/WebAssembly
- **Backend**: .NET Core API
- **Database**: SQL Server
- **Authentication**: Firebase Auth integration

## 📱 Mobile App Setup

### Prerequisites
- Flutter SDK (3.0 or higher)
- Android Studio / VS Code
- Android SDK (API level 36)
- Java 8 (JVM target 1.8)
- Firebase project setup

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone https://github.com/sunilvijayan7/GetDoctor.git
   cd GetDoctor/GetDoctor_Mobile
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Configuration**
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Add Android app to your Firebase project
   - Download `google-services.json` and place it in `android/app/`
   - Enable Authentication, Firestore, and other required services

4. **Android Configuration**
   - Ensure minimum SDK version is 36 (as configured)
   - JVM target is set to 1.8 for compatibility

5. **Run the application**
   ```bash
   # List available devices
   flutter devices

   # Run on connected device/emulator
   flutter run

   # Run in release mode
   flutter run --release
   ```

### Build Configuration

The app is configured with:
- **Minimum SDK**: 36
- **Target SDK**: 36
- **Compile SDK**: 36
- **JVM Target**: 1.8 (Java 8)
- **Kotlin JVM Target**: 1.8
- **MultiDex**: Enabled

### Recent Fixes & Improvements

✅ **Resolved Build Issues**:
- Fixed Dart null safety violations across all entities and models
- Resolved JVM target compatibility between Java and Kotlin compilation
- Fixed Android manifest configuration for Android 12+ compatibility
- Removed problematic fluttertoast plugin and replaced with native AlertDialog
- Updated dependency versions and resolved conflicts
- Fixed constructor parameter mismatches in dependency injection

✅ **Code Quality Improvements**:
- Enhanced null safety implementation
- Improved error handling with proper exception management
- Updated entity-model relationships for data consistency
- Optimized build configuration for better performance
## 🔧 Troubleshooting

### Common Build Issues

**JVM Target Compatibility Error**
```
Inconsistent JVM-target compatibility detected for tasks 'compileDebugJavaWithJavac' (1.8) and 'compileDebugKotlin' (21)
```
**Solution**: Already fixed in `android/app/build.gradle` with proper JVM target configuration.

**Null Safety Violations**
- All null safety issues have been resolved in the codebase
- Entities and models are properly aligned with required properties

**Plugin Compatibility Issues**
- Removed problematic `fluttertoast` plugin
- Replaced with native Flutter `AlertDialog` for better compatibility

**Android Manifest Issues**
- Added `android:exported="true"` for Android 12+ compatibility
- Configured proper permissions for location and internet access

### Development Commands

```bash
# Clean build files
flutter clean

# Get dependencies
flutter pub get

# Run with verbose output
flutter run --verbose

# Build APK
flutter build apk

# Build for release
flutter build apk --release

# Run tests
flutter test
```

## ✨ Features

<table>
<th><h3>Core Features</h3></th>
<th><h3>User Features</h3></th>
<th><h3>System Features</h3></th>
<tr>
<td>
<ul>
<li>✅ Online Appointment Booking</li>
<li>✅ Doctor Selection & Search</li>
<li>✅ Appointment Management</li>
<li>✅ Multi-role Support (Doctor/Patient/Staff)</li>
<li>✅ Real-time Chat Communication</li>
<li>✅ Location-based Services</li>
<li>✅ Firebase Authentication</li>
<li>✅ Google Maps Integration</li>
<li>✅ Push Notifications</li>
<li>✅ Rating & Review System</li>
<li>✅ Payment Gateway Integration</li>
<li>✅ Schedule Management</li>
<li>✅ Profile Management</li>
<li>✅ Specialty-based Filtering</li>
</ul>
</td>
<td>
<ul>
<li>🔐 Secure Login/Register</li>
<li>📱 Mobile Verification</li>
<li>🔒 Forgot Password Recovery</li>
<li>🏠 Dashboard/Home Screen</li>
<li>📅 Booking Management</li>
<li>🩺 Doctor Specialties</li>
<li>👤 User Profile</li>
<li>⭐ Doctor Ratings</li>
<li>🏥 Clinic Information</li>
<li>❤️ Favorites List</li>
<li>💬 In-app Chat</li>
<li>🔍 Advanced Search & Filters</li>
<li>⚙️ Settings & Preferences</li>
<li>💳 Secure Checkout</li>
<li>🔔 Smart Notifications</li>
</ul>
</td>
<td>
<ul>
<li>🌐 Cross-platform Compatibility</li>
<li>🎨 Modern UI/UX Design</li>
<li>🌙 Light & Dark Themes</li>
<li>🌍 Multi-language Support</li>
<li>📱 Responsive Design</li>
<li>🔄 Real-time Synchronization</li>
<li>🛡️ Data Security & Privacy</li>
<li>📊 Analytics Integration</li>
<li>🚀 Performance Optimized</li>
<li>🔧 Easy Maintenance</li>
<li>📚 Comprehensive Documentation</li>
<li>🆕 Regular Updates</li>
<li>🌐 Cloud-based Backend</li>
<li>📈 Scalable Architecture</li>
</ul>
</td>
</tr>
</table>


<h4> Mobile Screens </h4>
<p align="center">
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/1.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/2.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/3.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/4.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/5.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/6.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/7.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/8.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/9.png"  width="200" height="400"/>
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/10.png" width="200" height="400" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/11.png" width="200" height="400" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Mobile/12.png" width="200" height="400" />
</p>

<h4>Flow</h4>
<p align="center">
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Flow/Flow.JPG" />
</p>

<h4>Ideation</h4>
<p align="center">
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Ideation/1.png" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Ideation/2.png" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Ideation/3.png" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Ideation/4.png" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Ideation/5.png" />
</p>

<h4>Wireframes</h4>
<p align="center">
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Wireframes/1.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Wireframes/2.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Wireframes/3.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Wireframes/4.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Wireframes/5.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Wireframes/6.jpg" />
</p>

<h4> Desktop Screens </h4>
<p align="center">
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Desktop/Appointment.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Desktop/AddNew.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Desktop/Gender.jpg" />
<img src="https://raw.githubusercontent.com/sunilvijayan7/GetDoctor/main/Screenshots/Desktop/Patient.jpg" />
</p>
## 📁 Project Structure

```
GetDoctor/
├── GetDoctor_Mobile/          # Flutter Mobile Application
│   ├── lib/
│   │   ├── features/          # Feature-based modules
│   │   │   ├── data/         # Data layer (repositories, data sources)
│   │   │   ├── domain/       # Domain layer (entities, use cases)
│   │   │   └── presentation/ # Presentation layer (pages, cubits, widgets)
│   │   ├── injection_container.dart  # Dependency injection setup
│   │   └── main.dart         # App entry point
│   ├── android/              # Android-specific configuration
│   ├── ios/                  # iOS-specific configuration
│   └── assets/               # App assets (images, icons)
├── GetDoctor_Web/            # Web Application (.NET/Blazor)
├── Screenshots/              # App screenshots
├── Wireframes/              # Design wireframes
├── Flow/                    # User flow diagrams
└── Ideation/               # Design concepts
```

## 🏗️ Architecture

The mobile app follows **Clean Architecture** principles:

- **Presentation Layer**: UI components, state management (BLoC/Cubit)
- **Domain Layer**: Business logic, entities, use cases
- **Data Layer**: Data sources, repositories, models

### Key Design Patterns
- **BLoC Pattern**: For state management
- **Repository Pattern**: For data abstraction
- **Dependency Injection**: Using GetIt service locator
- **Clean Architecture**: For separation of concerns

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Developer

<p align="left">

- 💻 All projects available at [http://www.sunilvijayan.com/](http://www.sunilvijayan.com/)
- 📫 Contact: **<EMAIL>**
- 🌟 **IZI Health**: Advanced version at [https://www.izi.health](https://www.izi.health)

</p>

## 🙏 Acknowledgments

- Flutter team for the amazing framework
- Firebase for backend services
- Google Maps for location services
- All contributors and supporters of this project

---

<p align="center">
  <strong>⭐ Star this repository if you find it helpful!</strong>
</p>

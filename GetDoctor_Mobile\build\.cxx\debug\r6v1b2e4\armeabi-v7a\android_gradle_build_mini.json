{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\build\\.cxx\\debug\\r6v1b2e4\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\build\\.cxx\\debug\\r6v1b2e4\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
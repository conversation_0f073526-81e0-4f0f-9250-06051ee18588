class DoctorEntity {
  final String doctorName;
  final String specialist;
  final String timing;
  final String department;
  final String profileImage;
  final String gender;
  final String phoneNumber;
  final String email;
  final String descriptionDetails;
  final String hospitalAddress;
  final String qualification;
  final String doctorId;

  DoctorEntity({
    required this.doctorName,
    required this.specialist,
    required this.timing,
    required this.department,
    required this.profileImage,
    required this.gender,
    required this.phoneNumber,
    required this.email,
    required this.descriptionDetails,
    required this.hospitalAddress,
    required this.qualification,
    required this.doctorId,
  });
}

{"logs": [{"outputFile": "com.gribyassine.getdoctor.app-mergeDebugResources-31:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ce9588d11a1ef318240bbc000876080d\\transformed\\jetified-android-maps-utils-3.6.0\\res\\values\\values.xml", "from": {"startLines": "2,3,7,10", "startColumns": "4,4,4,4", "startOffsets": "55,93,301,461", "endLines": "2,6,9,14", "endColumns": "37,12,12,12", "endOffsets": "88,296,456,708"}, "to": {"startLines": "132,219,223,226", "startColumns": "4,4,4,4", "startOffsets": "6848,13574,13782,13942", "endLines": "132,222,225,230", "endColumns": "37,12,12,12", "endOffsets": "6881,13777,13937,14189"}}, {"source": "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,830", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "472,996"}, "to": {"startLines": "199,203", "startColumns": "4,4", "startOffsets": "12327,12508", "endLines": "202,205", "endColumns": "12,12", "endOffsets": "12503,12672"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1d42a15b305521b00ec0ead9d57f5e87\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "155", "startColumns": "4", "startOffsets": "8039", "endColumns": "42", "endOffsets": "8077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "63,64,65,66,67,68,69,70,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,360,436", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2313,2403,2483,2573,2663,2743,2824,2904,9042,9147,9328,9453,9560,9740,9863,9979,10249,10437,10542,10723,10848,11023,11171,11234,11296,18631,20453", "endLines": "63,64,65,66,67,68,69,70,170,171,172,173,174,175,176,177,179,180,181,182,183,184,185,186,187,372,454", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2398,2478,2568,2658,2738,2819,2899,2979,9142,9323,9448,9555,9735,9858,9974,10077,10432,10537,10718,10843,11018,11166,11229,11291,11370,18941,20865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3943c3daa37d64d60b591e6ec78d8073\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "134,138,159,312,317", "startColumns": "4,4,4,4,4", "startOffsets": "6946,7115,8246,17463,17633", "endLines": "134,138,159,316,320", "endColumns": "56,64,63,24,24", "endOffsets": "6998,7175,8305,17628,17777"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\13c2522d2cb4fb3d641d53c468f44d4f\\transformed\\jetified-activity-1.8.1\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "137,156", "startColumns": "4,4", "startOffsets": "7073,8082", "endColumns": "41,59", "endOffsets": "7110,8137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "57,58,59,60,73,74,188,189,190,191", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "1933,1991,2057,2120,3116,3187,11375,11443,11510,11589", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "1986,2052,2115,2177,3182,3254,11438,11505,11584,11653"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3b9284c470170d74414f94dc98b89b8e\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,3,9,17,26,38,44,50,51,52,53,54,133,231,237,455,463,478", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,164,337,556,835,1149,1337,1524,1577,1637,1689,1734,6886,14194,14389,20870,21152,21766", "endLines": "2,8,16,24,37,43,49,50,51,52,53,54,133,236,241,462,477,493", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,332,551,770,1144,1332,1519,1572,1632,1684,1729,1768,6941,14384,14542,21147,21761,22415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "25,55,56,61,62,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,135,136,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161,163,164,165,166,167,168,169,198,206,207,211,212,216,217,218,242,248,258,291,321,354", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "775,1773,1845,2182,2247,2984,3053,3259,3329,3397,3469,3539,3600,3674,3747,3808,3869,3931,3995,4057,4118,4186,4286,4346,4412,4485,4554,4611,4663,4725,4797,4873,4938,4997,5056,5116,5176,5236,5296,5356,5416,5476,5536,5596,5656,5715,5775,5835,5895,5955,6015,6075,6135,6195,6255,6315,6374,6434,6494,6553,6612,6671,6730,6789,7003,7038,7180,7235,7298,7353,7411,7469,7530,7593,7650,7701,7751,7812,7869,7935,7969,8004,8378,8531,8598,8670,8739,8808,8882,8954,12256,12677,12794,12995,13105,13306,13435,13507,14547,14750,15051,16782,17782,18464", "endLines": "25,55,56,61,62,71,72,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,135,136,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,161,163,164,165,166,167,168,169,198,206,210,211,215,216,217,218,247,257,290,311,353,359", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "830,1840,1928,2242,2308,3048,3111,3324,3392,3464,3534,3595,3669,3742,3803,3864,3926,3990,4052,4113,4181,4281,4341,4407,4480,4549,4606,4658,4720,4792,4868,4933,4992,5051,5111,5171,5231,5291,5351,5411,5471,5531,5591,5651,5710,5770,5830,5890,5950,6010,6070,6130,6190,6250,6310,6369,6429,6489,6548,6607,6666,6725,6784,6843,7033,7068,7230,7293,7348,7406,7464,7525,7588,7645,7696,7746,7807,7864,7930,7964,7999,8034,8443,8593,8665,8734,8803,8877,8949,9037,12322,12789,12990,13100,13301,13430,13502,13569,14745,15046,16777,17458,18459,18626"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f886222c74897d652e2c9506a518e149\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "158", "startColumns": "4", "startOffsets": "8196", "endColumns": "49", "endOffsets": "8241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db90d39193f1db113b01ff3f74e10fce\\transformed\\jetified-play-services-maps-18.2.0\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "167", "endLines": "66", "endColumns": "20", "endOffsets": "1669"}, "to": {"startLines": "373", "startColumns": "4", "startOffsets": "18946", "endLines": "435", "endColumns": "20", "endOffsets": "20448"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\228692b75745c98c18fa52f377ee7ba9\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "157", "startColumns": "4", "startOffsets": "8142", "endColumns": "53", "endOffsets": "8191"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8b5634de22240e81a4f4da6bf11fab58\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "162", "startColumns": "4", "startOffsets": "8448", "endColumns": "82", "endOffsets": "8526"}}, {"source": "C:\\Users\\<USER>\\Documents\\test\\GetDoctor\\GetDoctor_Mobile\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,577", "endColumns": "81,103,108,119,106,75", "endOffsets": "132,236,345,465,572,648"}, "to": {"startLines": "192,193,194,195,196,197", "startColumns": "4,4,4,4,4,4", "startOffsets": "11658,11740,11844,11953,12073,12180", "endColumns": "81,103,108,119,106,75", "endOffsets": "11735,11839,11948,12068,12175,12251"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "160,178", "startColumns": "4,4", "startOffsets": "8310,10082", "endColumns": "67,166", "endOffsets": "8373,10244"}}]}]}
{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Documents/test/GetDoctor/GetDoctor_Mobile/build/.cxx/debug/r6v1b2e4/arm64-v8a", "source": "C:/flutter/packages/flutter_tools/gradle/src/main/scripts"}, "version": {"major": 2, "minor": 3}}
1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.gribyassine.getdoctor"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="32"
9        android:targetSdkVersion="36" />
10    <!--
11         <PERSON><PERSON><PERSON> needs it to communicate with the running application
12         to allow setting breakpoints, to provide hot reload, etc.
13    -->
14    <uses-permission android:name="android.permission.INTERNET" />
14-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:3:5-67
14-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:3:22-64
15    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
15-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:4:5-79
15-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:4:22-76
16    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
16-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:5:5-81
16-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:5:22-78
17    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
17-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:6:5-85
17-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:6:22-82
18
19    <queries>
19-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-34:15
20        <package android:name="com.google.android.apps.maps" />
20-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-64
20-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-61
21        <package android:name="com.google.android.apps.mapslite" />
21-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-68
21-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:9:18-65
22        <package android:name="com.autonavi.minimap" />
22-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-56
22-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:10:18-53
23        <package android:name="com.baidu.BaiduMap" />
23-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-54
23-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:11:18-51
24        <package android:name="com.waze" />
24-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-44
24-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:12:18-41
25        <package android:name="ru.yandex.yandexnavi" />
25-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-56
25-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:13:18-53
26        <package android:name="ru.yandex.yandexmaps" />
26-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-56
26-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:14:18-53
27        <package android:name="com.citymapper.app.release" />
27-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-62
27-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:15:18-59
28        <package android:name="com.mapswithme.maps.pro" />
28-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-59
28-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:16:18-56
29        <package android:name="net.osmand" />
29-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-46
29-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:17:18-43
30        <package android:name="net.osmand.plus" />
30-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-51
30-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:18:18-48
31        <package android:name="ru.dublgis.dgismobile" />
31-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-57
31-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:19:18-54
32        <package android:name="com.tencent.map" />
32-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-51
32-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:20:18-48
33        <package android:name="com.here.app.maps" />
33-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-53
33-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:21:18-50
34        <package android:name="com.huawei.maps.app" />
34-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-55
34-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:22:18-52
35        <package android:name="com.alk.copilot.mapviewer" />
35-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:23:9-61
35-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:23:18-58
36        <package android:name="com.tomtom.gplay.navapp" />
36-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:24:9-59
36-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:24:18-56
37        <package android:name="com.tomtom.gplay.navapp.gofleet" />
37-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-67
37-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:25:18-64
38        <package android:name="com.sygic.truck" />
38-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-51
38-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:26:18-48
39        <package android:name="nl.flitsmeister" />
39-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:27:9-51
39-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:27:18-48
40        <package android:name="nl.flitsmeister.flux" />
40-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-56
40-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:28:18-53
41        <package android:name="com.nhn.android.nmap" />
41-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-56
41-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:29:18-53
42        <package android:name="net.daum.android.map" />
42-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:30:9-56
42-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:30:18-53
43        <package android:name="com.skt.tmap.ku" />
43-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:31:9-51
43-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:31:18-48
44        <package android:name="cz.seznam.mapy" />
44-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:32:9-50
44-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:32:18-47
45        <package android:name="com.mmi.maps" />
45-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:33:9-48
45-->[:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:33:18-45
46    </queries>
47
48    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
48-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
48-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
49
50    <uses-feature
50-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
51        android:glEsVersion="0x00020000"
51-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
52        android:required="true" />
52-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
53
54    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
54-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
54-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
55
56    <permission
56-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
57        android:name="com.gribyassine.getdoctor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="com.gribyassine.getdoctor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
61
62    <application
62-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:8:4-46:19
63        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
63-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
64        android:debuggable="true"
65        android:extractNativeLibs="false"
66        android:icon="@mipmap/ic_launcher"
66-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:10:9-43
67        android:label="getdoctor" >
67-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:9:9-34
68        <activity
68-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:11:9-40:20
69            android:name="com.gribyassine.getdoctor.MainActivity"
69-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:12:13-41
70            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
70-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:16:13-163
71            android:exported="true"
71-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:13:13-36
72            android:hardwareAccelerated="true"
72-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:17:13-47
73            android:launchMode="singleTop"
73-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:14:13-43
74            android:theme="@style/LaunchTheme"
74-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:15:13-47
75            android:windowSoftInputMode="adjustResize" >
75-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:18:13-55
76
77            <!--
78                 Specifies an Android theme to apply to this Activity as soon as
79                 the Android process has started. This theme is visible to the user
80                 while the Flutter UI initializes. After that, this theme continues
81                 to determine the Window background behind the Flutter UI.
82            -->
83            <meta-data
83-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:23:13-26:17
84                android:name="io.flutter.embedding.android.NormalTheme"
84-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:24:15-70
85                android:resource="@style/NormalTheme" />
85-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:25:15-52
86            <!--
87                 Displays an Android View that continues showing the launch screen
88                 Drawable until Flutter paints its first frame, then this splash
89                 screen fades out. A splash screen is useful to avoid any visual
90                 gap between the end of Android's launch screen and the painting of
91                 Flutter's first frame.
92            -->
93            <meta-data
93-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:32:13-35:17
94                android:name="io.flutter.embedding.android.SplashScreenDrawable"
94-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:33:15-79
95                android:resource="@drawable/launch_background" />
95-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:34:15-61
96
97            <intent-filter>
97-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:36:13-39:29
98                <action android:name="android.intent.action.MAIN" />
98-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:37:17-68
98-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:37:25-66
99
100                <category android:name="android.intent.category.LAUNCHER" />
100-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:38:17-76
100-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:38:27-74
101            </intent-filter>
102        </activity>
103        <!--
104             Don't delete the meta-data below.
105             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
106        -->
107        <meta-data
107-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:43:9-45:33
108            android:name="flutterEmbedding"
108-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:44:13-44
109            android:value="2" />
109-->C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:45:13-30
110
111        <service
111-->[:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
112            android:name="com.google.firebase.components.ComponentDiscoveryService"
112-->[:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
113            android:directBootAware="true"
113-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
114            android:exported="false" >
114-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
115            <meta-data
115-->[:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
116                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
116-->[:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
117                android:value="com.google.firebase.components.ComponentRegistrar" />
117-->[:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
118            <meta-data
118-->[:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
119                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
119-->[:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
120                android:value="com.google.firebase.components.ComponentRegistrar" />
120-->[:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
121            <meta-data
121-->[:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
122                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
122-->[:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
123                android:value="com.google.firebase.components.ComponentRegistrar" />
123-->[:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
124            <meta-data
124-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
125                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
125-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
126                android:value="com.google.firebase.components.ComponentRegistrar" />
126-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
127            <meta-data
127-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
128                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
128-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
129                android:value="com.google.firebase.components.ComponentRegistrar" />
129-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
130            <meta-data
130-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
131                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
131-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
132                android:value="com.google.firebase.components.ComponentRegistrar" />
132-->[com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
133            <meta-data
133-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
134                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
134-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
135                android:value="com.google.firebase.components.ComponentRegistrar" />
135-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
136            <meta-data
136-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
137                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
137-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
138                android:value="com.google.firebase.components.ComponentRegistrar" />
138-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
139        </service>
140        <service
140-->[:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
141            android:name="com.baseflow.geolocator.GeolocatorLocationService"
141-->[:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
142            android:enabled="true"
142-->[:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
143            android:exported="false"
143-->[:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
144            android:foregroundServiceType="location" />
144-->[:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
145        <service
145-->[:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-15:56
146            android:name="com.lyokone.location.FlutterLocationService"
146-->[:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-71
147            android:enabled="true"
147-->[:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-35
148            android:exported="false"
148-->[:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
149            android:foregroundServiceType="location" />
149-->[:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-53
150
151        <activity
151-->[:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
152            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
152-->[:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
153            android:exported="false"
153-->[:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
154            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
154-->[:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
155        <activity
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
156            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
157            android:excludeFromRecents="true"
157-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
158            android:exported="true"
158-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
159            android:launchMode="singleTask"
159-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
160            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
160-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
161            <intent-filter>
161-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
162                <action android:name="android.intent.action.VIEW" />
162-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
162-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
163
164                <category android:name="android.intent.category.DEFAULT" />
164-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
164-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
165                <category android:name="android.intent.category.BROWSABLE" />
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
165-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
166
167                <data
167-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
168                    android:host="firebase.auth"
168-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
169                    android:path="/"
169-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
170                    android:scheme="genericidp" />
170-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
171            </intent-filter>
172        </activity>
173        <activity
173-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
174            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
174-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
175            android:excludeFromRecents="true"
175-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
176            android:exported="true"
176-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
177            android:launchMode="singleTask"
177-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
178            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
178-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
179            <intent-filter>
179-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
180                <action android:name="android.intent.action.VIEW" />
180-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
180-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
181
182                <category android:name="android.intent.category.DEFAULT" />
182-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
182-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
183                <category android:name="android.intent.category.BROWSABLE" />
183-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
183-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
184
185                <data
185-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
186                    android:host="firebase.auth"
186-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
187                    android:path="/"
187-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
188                    android:scheme="recaptcha" />
188-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
189            </intent-filter>
190        </activity>
191
192        <uses-library
192-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
193            android:name="androidx.window.extensions"
193-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
194            android:required="false" />
194-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
195        <uses-library
195-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
196            android:name="androidx.window.sidecar"
196-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
197            android:required="false" /> <!-- Needs to be explicitly declared on P+ -->
197-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
198        <uses-library
198-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
199            android:name="org.apache.http.legacy"
199-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
200            android:required="false" />
200-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
201
202        <activity
202-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
203            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
203-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
204            android:excludeFromRecents="true"
204-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
205            android:exported="false"
205-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
206            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
206-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
207        <!--
208            Service handling Google Sign-In user revocation. For apps that do not integrate with
209            Google Sign-In, this service will never be started.
210        -->
211        <service
211-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
212            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
212-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
213            android:exported="true"
213-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
214            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
214-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
215            android:visibleToInstantApps="true" />
215-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
216
217        <activity
217-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
218            android:name="com.google.android.gms.common.api.GoogleApiActivity"
218-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
219            android:exported="false"
219-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
220            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
220-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
221
222        <provider
222-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
223            android:name="com.google.firebase.provider.FirebaseInitProvider"
223-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
224            android:authorities="com.gribyassine.getdoctor.firebaseinitprovider"
224-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
225            android:directBootAware="true"
225-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
226            android:exported="false"
226-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
227            android:initOrder="100" />
227-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
228
229        <meta-data
229-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
230            android:name="com.google.android.gms.version"
230-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
231            android:value="@integer/google_play_services_version" />
231-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
232
233        <provider
233-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
234            android:name="androidx.startup.InitializationProvider"
234-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
235            android:authorities="com.gribyassine.getdoctor.androidx-startup"
235-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
236            android:exported="false" >
236-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
237            <meta-data
237-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
238                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
238-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
239                android:value="androidx.startup" />
239-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
240            <meta-data
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
241                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
242                android:value="androidx.startup" />
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
243        </provider>
244
245        <receiver
245-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
246            android:name="androidx.profileinstaller.ProfileInstallReceiver"
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
247            android:directBootAware="false"
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
248            android:enabled="true"
248-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
249            android:exported="true"
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
250            android:permission="android.permission.DUMP" >
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
251            <intent-filter>
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
252                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
253            </intent-filter>
254            <intent-filter>
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
255                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
256            </intent-filter>
257            <intent-filter>
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
258                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
259            </intent-filter>
260            <intent-filter>
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
261                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
261-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
262            </intent-filter>
263        </receiver>
264    </application>
265
266</manifest>

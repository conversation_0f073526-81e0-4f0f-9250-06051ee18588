{"logs": [{"outputFile": "com.gribyassine.getdoctor.app-mergeDebugResources-31:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "783,890,1047,1174,1284,1425,1550,1673,1925,2073,2181,2343,2471,2625,2781,2847,2910", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "885,1042,1169,1279,1420,1545,1668,1772,2068,2176,2338,2466,2620,2776,2842,2905,2984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,3400", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,3496"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1777", "endColumns": "147", "endOffsets": "1920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2989,3089,3189,3302", "endColumns": "99,99,112,97", "endOffsets": "3084,3184,3297,3395"}}]}]}
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:getdoctor/features/domain/entities/text_messsage_entity.dart';

class TextMessageModel extends TextMessageEntity {
  TextMessageModel({
    required String senderName,
    required String senderUID,
    required String recipientName,
    required String recipientUID,
    required String message,
    required Timestamp time,
    required String type,
    required bool isRead,
    required String messageId,
    required String profileURL,
    required String channelId,
    required bool isOPD,
  }) : super(
          senderName: senderName,
          senderUID: senderUID,
          recipientName: recipientName,
          recipientUID: recipientUID,
          message: message,
          time: time,
          type: type,
          isRead: isRead,
          messageId: messageId,
          profileURL: profileURL,
          channelId: channelId,
          isOPD: isOPD,
        );

  factory TextMessageModel.fromSnapShot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return TextMessageModel(
      senderName: data['senderName'] ?? '',
      senderUID: data['senderUID'] ?? '',
      recipientName: data['recipientName'] ?? '',
      recipientUID: data['recipientUID'] ?? '',
      message: data['message'] ?? '',
      time: data['time'] ?? Timestamp.now(),
      type: data['type'] ?? '',
      isRead: data['isRead'] ?? false,
      messageId: data['messageId'] ?? '',
      profileURL: data['profileURL'] ?? '',
      channelId: data['channelId'] ?? '',
      isOPD: data['isOPD'] ?? false,
    );
  }

  Map<String,dynamic> toDocument(){
    return {
      "senderName":senderName,
      "senderUID":senderUID,
      "recipientName":recipientName,
      "recipientUID":recipientUID,
      "message":message,
      "time":time,
      "type":type,
      "isRead":isRead,
      "messageId":messageId,
      "profileURL":profileURL,
      "channelId":channelId,
      "isOPD":isOPD,
    };
  }
}
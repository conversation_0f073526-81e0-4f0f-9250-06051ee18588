-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:1:1-47:12
MERGED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:1:1-47:12
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml:1:1-6:12
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml:1:1-6:12
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml:1:1-6:12
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:geocoding_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geocoding_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-18:12
MERGED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-36:12
MERGED from [:maps_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\maps_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:path_provider_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:sqflite_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [:webview_flutter_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6803759c34167ac9485b4e0435e9db50\transformed\multidex-2.0.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\087a8b2cfdcd65a1db6a23a64b74ad04\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:2:1-26:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:2:1-18:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\3cfadcd14441af80fcd00bbc10ab581f\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\761f428a3af83b247505caa77bfa9d50\transformed\webkit-1.12.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b11a5ce3e859912b146223f3564e055\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:17:1-21:12
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:17:1-40:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5cff4c0261f8de506117d3680ae9cd0c\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\6f2865dbbdd2dbc3fae49c5f6073e026\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\402020b74f3937ef665817ee394a5f6a\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5874504db7a9a9ebb6bd19f758031e81\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\d577bd41c5d39e66f0e6bb010338713a\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:16:1-24:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\341c80673bae293c6030b99b16d7fa07\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6170572ba297c6256082e1ff88cb3d5b\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7464f42da2d1e143966deca92bcf6f77\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\3943c3daa37d64d60b591e6ec78d8073\transformed\fragment-1.7.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cdcae166ee10c3bee49a3e65b0e7cdf\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\13c2522d2cb4fb3d641d53c468f44d4f\transformed\jetified-activity-1.8.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f886222c74897d652e2c9506a518e149\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\45a9bdc8bd8af3d4b665063ce5d7948c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\226df710d62ef45d0cf0d9cff79a3ad6\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\d1656754ed215a798edd93ad34d7714e\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98899d38dbec2fb95b84f3dad2eda5e8\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc18b572e9ee1187789d0ad1f672c081\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d42a15b305521b00ec0ead9d57f5e87\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\228692b75745c98c18fa52f377ee7ba9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca25c0ec91ded603cbe9b97c24d866a2\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c64eeccfcf995e5fb76d61ec6b15475\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\270b58da983cea4e3f74568bb8bfc0f7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd556d4cd7d27616cf4a227f9cec191\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\48e008685135e228736a8cffb4526392\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\027e4a4ceb9b9b85b18f750c8923eb2f\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19f003c02e47e83ea69d6e1b1319bef3\transformed\jetified-core-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\713da0d3f209ff142ae559f65ebe5f4d\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\8acd9d24b0f7c11d9ffbb1dab8b55737\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:2:1-7:12
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\7853a39af8bd4ced328a779801bc2b58\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\24bf96f764b5090dc2b020e980acaa91\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:3:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:11:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:3:22-64
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:4:5-79
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-79
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-79
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:4:22-76
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:5:5-81
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-81
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:5:22-78
uses-permission#android.permission.ACCESS_BACKGROUND_LOCATION
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:6:5-85
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:6:22-82
application
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:8:4-46:19
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:19
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-16:19
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-16:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-12:19
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\087a8b2cfdcd65a1db6a23a64b74ad04\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\087a8b2cfdcd65a1db6a23a64b74ad04\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:13:5-24:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:8:5-16:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:22:5-29:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:36:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\402020b74f3937ef665817ee394a5f6a\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\402020b74f3937ef665817ee394a5f6a\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5874504db7a9a9ebb6bd19f758031e81\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5874504db7a9a9ebb6bd19f758031e81\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\341c80673bae293c6030b99b16d7fa07\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\341c80673bae293c6030b99b16d7fa07\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6170572ba297c6256082e1ff88cb3d5b\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6170572ba297c6256082e1ff88cb3d5b\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7464f42da2d1e143966deca92bcf6f77\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7464f42da2d1e143966deca92bcf6f77\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\270b58da983cea4e3f74568bb8bfc0f7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\270b58da983cea4e3f74568bb8bfc0f7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:7:5-11:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:9:9-34
	android:icon
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:10:9-43
activity#com.gribyassine.getdoctor.MainActivity
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:11:9-40:20
	android:launchMode
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:14:13-43
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:17:13-47
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:18:13-55
	android:exported
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:13:13-36
	android:configChanges
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:16:13-163
	android:theme
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:15:13-47
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:12:13-41
meta-data#io.flutter.embedding.android.NormalTheme
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:23:13-26:17
	android:resource
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:25:15-52
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:24:15-70
meta-data#io.flutter.embedding.android.SplashScreenDrawable
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:32:13-35:17
	android:resource
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:34:15-61
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:33:15-79
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:36:13-39:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:37:17-68
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:37:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:38:17-76
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:38:27-74
meta-data#flutterEmbedding
ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:43:9-45:33
	android:value
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:45:13-30
	android:name
		ADDED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\main\AndroidManifest.xml:44:13-44
uses-sdk
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_maps_flutter_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_maps_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:flutter_plugin_android_lifecycle] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geocoding_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:geocoding_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geocoding_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:google_sign_in_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\google_sign_in_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:maps_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\maps_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:maps_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\maps_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:path_provider_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\path_provider_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:permission_handler_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\permission_handler_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:sqflite_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\sqflite_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:webview_flutter_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\webview_flutter_android\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6803759c34167ac9485b4e0435e9db50\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [androidx.multidex:multidex:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\6803759c34167ac9485b4e0435e9db50\transformed\multidex-2.0.1\AndroidManifest.xml:20:5-43
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\087a8b2cfdcd65a1db6a23a64b74ad04\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-location:21.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\087a8b2cfdcd65a1db6a23a64b74ad04\transformed\jetified-play-services-location-21.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:6:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\3cfadcd14441af80fcd00bbc10ab581f\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-3\3cfadcd14441af80fcd00bbc10ab581f\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\761f428a3af83b247505caa77bfa9d50\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.12.1] C:\Users\<USER>\.gradle\caches\transforms-3\761f428a3af83b247505caa77bfa9d50\transformed\webkit-1.12.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b11a5ce3e859912b146223f3564e055\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [androidx.window:window-java:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b11a5ce3e859912b146223f3564e055\transformed\jetified-window-java-1.2.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5cff4c0261f8de506117d3680ae9cd0c\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\transforms-3\5cff4c0261f8de506117d3680ae9cd0c\transformed\jetified-play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\6f2865dbbdd2dbc3fae49c5f6073e026\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\transforms-3\6f2865dbbdd2dbc3fae49c5f6073e026\transformed\jetified-play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\402020b74f3937ef665817ee394a5f6a\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\402020b74f3937ef665817ee394a5f6a\transformed\jetified-play-services-fido-20.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5874504db7a9a9ebb6bd19f758031e81\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5874504db7a9a9ebb6bd19f758031e81\transformed\jetified-firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\d577bd41c5d39e66f0e6bb010338713a\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\d577bd41c5d39e66f0e6bb010338713a\transformed\jetified-firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\341c80673bae293c6030b99b16d7fa07\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\341c80673bae293c6030b99b16d7fa07\transformed\jetified-firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:19:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6170572ba297c6256082e1ff88cb3d5b\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\6170572ba297c6256082e1ff88cb3d5b\transformed\jetified-integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7464f42da2d1e143966deca92bcf6f77\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7464f42da2d1e143966deca92bcf6f77\transformed\jetified-play-services-tasks-18.1.0\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\3943c3daa37d64d60b591e6ec78d8073\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.7.1] C:\Users\<USER>\.gradle\caches\transforms-3\3943c3daa37d64d60b591e6ec78d8073\transformed\fragment-1.7.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cdcae166ee10c3bee49a3e65b0e7cdf\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\6cdcae166ee10c3bee49a3e65b0e7cdf\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\13c2522d2cb4fb3d641d53c468f44d4f\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.1] C:\Users\<USER>\.gradle\caches\transforms-3\13c2522d2cb4fb3d641d53c468f44d4f\transformed\jetified-activity-1.8.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f886222c74897d652e2c9506a518e149\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\f886222c74897d652e2c9506a518e149\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\45a9bdc8bd8af3d4b665063ce5d7948c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\45a9bdc8bd8af3d4b665063ce5d7948c\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\226df710d62ef45d0cf0d9cff79a3ad6\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\226df710d62ef45d0cf0d9cff79a3ad6\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\d1656754ed215a798edd93ad34d7714e\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\d1656754ed215a798edd93ad34d7714e\transformed\jetified-core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98899d38dbec2fb95b84f3dad2eda5e8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\98899d38dbec2fb95b84f3dad2eda5e8\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc18b572e9ee1187789d0ad1f672c081\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bc18b572e9ee1187789d0ad1f672c081\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d42a15b305521b00ec0ead9d57f5e87\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1d42a15b305521b00ec0ead9d57f5e87\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\228692b75745c98c18fa52f377ee7ba9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\228692b75745c98c18fa52f377ee7ba9\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca25c0ec91ded603cbe9b97c24d866a2\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\ca25c0ec91ded603cbe9b97c24d866a2\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c64eeccfcf995e5fb76d61ec6b15475\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9c64eeccfcf995e5fb76d61ec6b15475\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\270b58da983cea4e3f74568bb8bfc0f7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\270b58da983cea4e3f74568bb8bfc0f7\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd556d4cd7d27616cf4a227f9cec191\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0bd556d4cd7d27616cf4a227f9cec191\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\48e008685135e228736a8cffb4526392\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\transforms-3\48e008685135e228736a8cffb4526392\transformed\jetified-firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\027e4a4ceb9b9b85b18f750c8923eb2f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\027e4a4ceb9b9b85b18f750c8923eb2f\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19f003c02e47e83ea69d6e1b1319bef3\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.window.extensions.core:core:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\19f003c02e47e83ea69d6e1b1319bef3\transformed\jetified-core-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\713da0d3f209ff142ae559f65ebe5f4d\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\713da0d3f209ff142ae559f65ebe5f4d\transformed\jetified-annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\8acd9d24b0f7c11d9ffbb1dab8b55737\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\transforms-3\8acd9d24b0f7c11d9ffbb1dab8b55737\transformed\jetified-relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\7853a39af8bd4ced328a779801bc2b58\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\7853a39af8bd4ced328a779801bc2b58\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\24bf96f764b5090dc2b020e980acaa91\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.firebase:protolite-well-known-types:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\24bf96f764b5090dc2b020e980acaa91\transformed\jetified-protolite-well-known-types-18.0.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\android\app\src\debug\AndroidManifest.xml
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:14:9-23:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar
ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:cloud_firestore] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar
ADDED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_auth] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
meta-data#com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar
ADDED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
	android:value
		ADDED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
	android:name
		ADDED from [:firebase_core] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
service#com.baseflow.geolocator.GeolocatorLocationService
ADDED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:56
	android:enabled
		ADDED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-35
	android:exported
		ADDED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
	android:foregroundServiceType
		ADDED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-53
	android:name
		ADDED from [:geolocator_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\geolocator_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-77
service#com.lyokone.location.FlutterLocationService
ADDED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-15:56
	android:enabled
		ADDED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-35
	android:exported
		ADDED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-37
	android:foregroundServiceType
		ADDED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-53
	android:name
		ADDED from [:location] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\location\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-71
queries
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:30:5-34:15
package#com.google.android.apps.maps
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-64
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-61
package#com.google.android.apps.mapslite
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-68
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:9:18-65
package#com.autonavi.minimap
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:10:9-56
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:10:18-53
package#com.baidu.BaiduMap
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:11:9-54
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:11:18-51
package#com.waze
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-44
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:12:18-41
package#ru.yandex.yandexnavi
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:13:9-56
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:13:18-53
package#ru.yandex.yandexmaps
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:14:9-56
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:14:18-53
package#com.citymapper.app.release
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-62
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:15:18-59
package#com.mapswithme.maps.pro
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:16:9-59
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:16:18-56
package#net.osmand
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:17:9-46
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:17:18-43
package#net.osmand.plus
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-51
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:18:18-48
package#ru.dublgis.dgismobile
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-57
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:19:18-54
package#com.tencent.map
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:20:9-51
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:20:18-48
package#com.here.app.maps
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-53
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:21:18-50
package#com.huawei.maps.app
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:22:9-55
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:22:18-52
package#com.alk.copilot.mapviewer
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:23:9-61
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:23:18-58
package#com.tomtom.gplay.navapp
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:24:9-59
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:24:18-56
package#com.tomtom.gplay.navapp.gofleet
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:25:9-67
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:25:18-64
package#com.sygic.truck
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:26:9-51
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:26:18-48
package#nl.flitsmeister
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:27:9-51
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:27:18-48
package#nl.flitsmeister.flux
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:28:9-56
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:28:18-53
package#com.nhn.android.nmap
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:29:9-56
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:29:18-53
package#net.daum.android.map
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:30:9-56
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:30:18-53
package#com.skt.tmap.ku
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:31:9-51
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:31:18-48
package#cz.seznam.mapy
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:32:9-50
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:32:18-47
package#com.mmi.maps
ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:33:9-48
	android:name
		ADDED from [:map_launcher] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\map_launcher\intermediates\merged_manifest\debug\AndroidManifest.xml:33:18-45
activity#io.flutter.plugins.urllauncher.WebViewActivity
ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-11:74
	android:exported
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-37
	android:theme
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-71
	android:name
		ADDED from [:url_launcher_android] C:\Users\<USER>\Documents\test\GetDoctor\GetDoctor_Mobile\build\url_launcher_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-74
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:10:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\7853a39af8bd4ced328a779801bc2b58\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
MERGED from [io.grpc:grpc-android:1.57.2] C:\Users\<USER>\.gradle\caches\transforms-3\7853a39af8bd4ced328a779801bc2b58\transformed\jetified-grpc-android-1.57.2\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e10bf11d1d75ff891f3998fe055342ca\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:17:13-19:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:19:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:18:17-122
meta-data#com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar
ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:20:13-22:85
	android:value
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:22:17-82
	android:name
		ADDED from [com.google.firebase:firebase-firestore:24.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\5a02190c9a38f86665aab5c372710442\transformed\jetified-firebase-firestore-24.11.0\AndroidManifest.xml:21:17-111
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\8bc71593fa13114135fc4963bcf3ce67\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
	android:required
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
	android:name
		ADDED from [androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b9284c470170d74414f94dc98b89b8e\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
uses-feature#0x00020000
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
	android:glEsVersion
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
uses-library#org.apache.http.legacy
ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
	android:required
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\db90d39193f1db113b01ff3f74e10fce\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\3778c63841cf471867d49ac28a13e9e1\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\077e275d279251ce9d9e5d312980d285\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\transforms-3\46be6f891d66e6c6f98c48daad864b22\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\a00e51e0fa140351d793476b966e36fd\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
MERGED from [com.google.maps.android:android-maps-utils:3.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\ce9588d11a1ef318240bbc000876080d\transformed\jetified-android-maps-utils-3.6.0\AndroidManifest.xml:8:9-10:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\b616328c7ec3c492f415b1fcfd369907\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.gribyassine.getdoctor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.gribyassine.getdoctor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\8ec732bac5f4544a105636aff7ea2555\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
provider#androidx.startup.InitializationProvider
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b5634de22240e81a4f4da6bf11fab58\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a24b4eac29dd251d8765ec2b38b9f5f8\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\695f736766bfce0b14e5efdd0fbe8201\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92

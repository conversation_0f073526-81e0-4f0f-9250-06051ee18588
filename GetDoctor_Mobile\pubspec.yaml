name: getdoctor
description: <PERSON><PERSON> and Get <PERSON> Appointment

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_svg: ^2.0.10
  bottom_navy_bar: ^6.0.0
  page_transition: ^2.1.0
  flutter_vector_icons: ^2.0.0  # Replacement for flutter_icons
  # flutter_icons: ^2.0.0  # No Dart 3 compatible version. Migrate to flutter_vector_icons or another maintained icon package if needed.
  google_maps_flutter: ^2.6.0
  google_sign_in: ^6.2.1
  flutter_bloc: ^8.1.4
  equatable: ^2.0.5
  get_it: ^7.6.7
  fluttertoast: ^8.2.4
  flutter_spinkit: ^5.2.0
  geolocator: ^10.1.0
  permission_handler: ^11.3.1
  webview_flutter: ^4.7.0  # Modern replacement for flutter_webview_plugin
  cached_network_image: ^3.3.1
  datetime_picker_formfield: ^2.0.1
  maps_launcher: ^3.0.0+1
  geocoding: ^2.1.1  # Replacement for geocoder
  # geocoder: ^0.2.1  # No null safety or Dart 3 support. Migrate to geocoding or another maintained package if needed.
  map_launcher: ^3.2.0
  bubble: ^1.2.1
  #firebase
  firebase_auth: ^4.17.8
  cloud_firestore: ^4.17.5
  intl: ^0.19.0
  location: ^6.0.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/.

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

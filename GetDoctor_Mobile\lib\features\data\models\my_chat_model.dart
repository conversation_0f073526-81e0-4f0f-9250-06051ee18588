import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:getdoctor/features/domain/entities/my_chat_entity.dart';

class MyChatModel extends MyChatEntity {
  MyChatModel({
    required String senderName,
    required String senderUID,
    required String senderPhoneNumber,
    required String recipientName,
    required String recipientUID,
    required String recipientPhoneNumber,
    required String recentTextMessage,
    required String profileURL,
    required bool isRead,
    required bool isArchived,
    required String channelId,
    required Timestamp time,
  }) : super(
          senderName: senderName,
          senderUID: senderUID,
          senderPhoneNumber: senderPhoneNumber,
          recipientName: recipientName,
          recipientUID: recipientUID,
          recipientPhoneNumber: recipientPhoneNumber,
          recentTextMessage: recentTextMessage,
          profileURL: profileURL,
          isRead: isRead,
          isArchived: isArchived,
          channelId: channelId,
          time: time,
        );

  factory MyChatModel.fromSnapShot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return MyChatModel(
      senderName: data['senderName'] ?? '',
      senderUID: data['senderUID'] ?? '',
      senderPhoneNumber: data['senderPhoneNumber'] ?? '',
      recipientName: data['recipientName'] ?? '',
      recipientUID: data['recipientUID'] ?? '',
      recipientPhoneNumber: data['recipientPhoneNumber'] ?? '',
      channelId: data['channelId'] ?? '',
      time: data['time'] ?? Timestamp.now(),
      isArchived: data['isArchived'] ?? false,
      isRead: data['isRead'] ?? false,
      recentTextMessage: data['recentTextMessage'] ?? '',
      profileURL: data['profileURL'] ?? '',
    );
  }

  Map<String, dynamic> toDocument() {
    return {
      "senderName": senderName,
      "senderUID": senderUID,
      "recipientName": recipientName,
      "recipientUID": recipientUID,
      "channelId": channelId,
      "profileURL": profileURL,
      "recipientPhoneNumber": recipientPhoneNumber,
      "senderPhoneNumber": senderPhoneNumber,
      "recentTextMessage": recentTextMessage,
      "isRead": isRead,
      "isArchived": isArchived,
      "time": time,
    };
  }
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:getdoctor/features/domain/entities/hospital_entity.dart';

class HospitalModel extends HospitalEntity {
  HospitalModel({
    required String hospitalFullAddress,
    required String hospitalPicUrl,
    required String emergency,
    required String hospitalId,
    required String hospitalName,
    required GeoPoint location,
    required List<String> departments,
  })
      : super(
          hospitalFullAddress: hospitalFullAddress,
          hospitalPicUrl: hospitalPicUrl,
          emergency: emergency,
          hospitalId: hospitalId,
          hospitalName: hospitalName,
          location: location,
          departments: departments,
        );

  factory HospitalModel.fromSnapShot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return HospitalModel(
      hospitalFullAddress: data['hospitalFullAddress'] ?? '',
      hospitalPicUrl: data['hospitalPicUrl'] ?? '',
      emergency: data['emergency'] ?? '',
      hospitalId: data['hospitalId'] ?? '',
      hospitalName: data['hospitalName'] ?? '',
      location: data['location'] ?? const GeoPoint(0, 0),
      departments: List<String>.from(data['departments'] ?? []),
    );
  }
}

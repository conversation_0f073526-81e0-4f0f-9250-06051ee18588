import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';

class TextMessageEntity extends Equatable {
  final String senderName;
  final String senderUID;
  final String recipientName;
  final String recipientUID;
  final String type;
  final String message;
  final bool isOPD;
  final String messageId;
  final Timestamp time;

  TextMessageEntity({
    required this.senderName,
    required this.senderUID,
    required this.recipientName,
    required this.recipientUID,
    required this.type,
    required this.message,
    this.isOPD = false,
    required this.messageId,
    required this.time,
  });

  @override
  List<Object> get props => [
    senderName,
    senderUID,
    recipientName,
    recipientUID,
    type,
    message,
    isOPD,
    messageId,
    time,
  ];
}
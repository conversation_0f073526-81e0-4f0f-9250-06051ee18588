{"logs": [{"outputFile": "com.gribyassine.getdoctor.app-mergeDebugResources-31:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8ec732bac5f4544a105636aff7ea2555\\transformed\\core-1.13.1\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,3484", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,3580"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\077e275d279251ce9d9e5d312980d285\\transformed\\jetified-play-services-base-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,442,565,669,832,958,1076,1177,1343,1447,1607,1733,1886,2039,2104,2166", "endColumns": "100,143,122,103,162,125,117,100,165,103,159,125,152,152,64,61,79", "endOffsets": "297,441,564,668,831,957,1075,1176,1342,1446,1606,1732,1885,2038,2103,2165,2245"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "790,895,1043,1170,1278,1445,1575,1697,1947,2117,2225,2389,2519,2676,2833,2902,2968", "endColumns": "104,147,126,107,166,129,121,104,169,107,163,129,156,156,68,65,83", "endOffsets": "890,1038,1165,1273,1440,1570,1692,1797,2112,2220,2384,2514,2671,2828,2897,2963,3047"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b616328c7ec3c492f415b1fcfd369907\\transformed\\jetified-play-services-basement-18.3.0\\res\\values-pt-rBR\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1802", "endColumns": "144", "endOffsets": "1942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3cfadcd14441af80fcd00bbc10ab581f\\transformed\\browser-1.8.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,269,381", "endColumns": "114,98,111,105", "endOffsets": "165,264,376,482"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3052,3167,3266,3378", "endColumns": "114,98,111,105", "endOffsets": "3162,3261,3373,3479"}}]}]}